<?php
require_once 'config.php';
require_once 'send_birthday_reminders.php';

// Test the fixed birthday reminder script
echo "<h2>Testing Birthday Reminder Fix</h2>";

// Get a member to test with
$query = "SELECT * FROM members WHERE id = 52 LIMIT 1"; // <PERSON>
$stmt = $pdo->prepare($query);
$stmt->execute();
$member = $stmt->fetch(PDO::FETCH_ASSOC);

if (!$member) {
    echo "<p>Member not found!</p>";
    exit;
}

echo "<h3>Testing Member: " . htmlspecialchars($member['full_name']) . "</h3>";

// Get a birthday template
$query = "SELECT * FROM email_templates WHERE is_birthday_template = 1 LIMIT 1";
$stmt = $pdo->prepare($query);
$stmt->execute();
$template = $stmt->fetch(PDO::FETCH_ASSOC);

if (!$template) {
    echo "<p>No birthday template found!</p>";
    exit;
}

echo "<h3>Using Template: " . htmlspecialchars($template['template_name']) . "</h3>";

// Create an instance of the BirthdayReminder class
$reminderSystem = new BirthdayReminder($pdo);

// Test the processTemplate method
echo "<h3>Testing processTemplate method:</h3>";

// Use reflection to access the private method
$reflection = new ReflectionClass($reminderSystem);
$processTemplateMethod = $reflection->getMethod('processTemplate');
$processTemplateMethod->setAccessible(true);

// Test the template processing
$processedContent = $processTemplateMethod->invoke($reminderSystem, $template['content'], $member, 0);

echo "<h4>Original Template Content (first 300 chars):</h4>";
echo "<pre>" . htmlspecialchars(substr($template['content'], 0, 300)) . "</pre>";

echo "<h4>Processed Content (first 500 chars):</h4>";
echo "<pre>" . htmlspecialchars(substr($processedContent, 0, 500)) . "</pre>";

// Check if the placeholder was replaced
$placeholderExists = strpos($processedContent, '{birthday_member_image_url}') !== false;
echo "<h4>Placeholder {birthday_member_image_url} still exists: " . ($placeholderExists ? 'YES' : 'NO') . "</h4>";

if ($placeholderExists) {
    echo "<p style='color: red;'><strong>ERROR: Placeholder was not replaced!</strong></p>";
} else {
    echo "<p style='color: green;'><strong>SUCCESS: Placeholder was replaced!</strong></p>";
}

// Show what the birthday_member_image_url was replaced with
if (preg_match('/src="([^"]*)"/', $processedContent, $matches)) {
    echo "<h4>Image URL found in processed content:</h4>";
    echo "<pre>" . htmlspecialchars($matches[1]) . "</pre>";
}
?>
