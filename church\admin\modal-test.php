<?php
session_start();

// Check if user is logged in
if (!isset($_SESSION['admin_id'])) {
    header("Location: login.php");
    exit();
}

// Include the configuration file
require_once '../config.php';

// Set page variables
$page_title = 'Modal Test';
$page_header = 'Modal Z-Index Test';
$page_description = 'Test page to verify modal z-index and positioning fixes';

// Include header
include 'includes/header.php';
?>

<!-- Test Instructions -->
<div class="alert alert-info mb-4">
    <h5><i class="bi bi-info-circle-fill me-2"></i>Modal Test Instructions</h5>
    <p>This page tests the modal z-index and positioning fixes. Try the following:</p>
    <ul>
        <li>Open a single modal - it should appear above the sidebar</li>
        <li>Open multiple modals - they should stack properly</li>
        <li>Test on different screen sizes (desktop, tablet, mobile)</li>
        <li>Verify backdrops work correctly</li>
    </ul>
</div>

<!-- Test Buttons -->
<div class="row mb-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h5>Modal Tests</h5>
            </div>
            <div class="card-body">
                <div class="d-flex flex-wrap gap-3">
                    <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#testModal1">
                        <i class="bi bi-window me-1"></i>Test Modal 1
                    </button>
                    <button type="button" class="btn btn-success" data-bs-toggle="modal" data-bs-target="#testModal2">
                        <i class="bi bi-window-stack me-1"></i>Test Modal 2
                    </button>
                    <button type="button" class="btn btn-warning" onclick="openNestedModal()">
                        <i class="bi bi-layers me-1"></i>Test Nested Modal
                    </button>
                    <button type="button" class="btn btn-info" onclick="openLargeModal()">
                        <i class="bi bi-window-fullscreen me-1"></i>Test Large Modal
                    </button>
                    <button type="button" class="btn btn-secondary" onclick="testModalManager()">
                        <i class="bi bi-gear me-1"></i>Test Modal Manager
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Test Modal 1 -->
<div class="modal fade" id="testModal1" tabindex="-1" aria-labelledby="testModal1Label" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="testModal1Label">Test Modal 1</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>This is the first test modal. It should appear above the sidebar with proper z-index.</p>
                <p>Z-index should be: <strong>1050</strong></p>
                <p>Backdrop z-index should be: <strong>1040</strong></p>
                <button type="button" class="btn btn-success" data-bs-toggle="modal" data-bs-target="#testModal2">
                    Open Modal 2 (Stacking Test)
                </button>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<!-- Test Modal 2 -->
<div class="modal fade" id="testModal2" tabindex="-1" aria-labelledby="testModal2Label" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="testModal2Label">Test Modal 2</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>This is the second test modal. When opened from Modal 1, it should stack on top.</p>
                <p>Z-index should be: <strong>1055</strong></p>
                <p>Backdrop z-index should be: <strong>1045</strong></p>
                <div class="alert alert-warning">
                    <strong>Stacking Test:</strong> This modal should appear above Modal 1 if both are open.
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<!-- Large Modal -->
<div class="modal fade" id="largeModal" tabindex="-1" aria-labelledby="largeModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="largeModalLabel">Large Modal Test</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>This is a large modal to test responsive behavior and scrolling.</p>
                <div class="row">
                    <div class="col-md-6">
                        <h6>Content Column 1</h6>
                        <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.</p>
                    </div>
                    <div class="col-md-6">
                        <h6>Content Column 2</h6>
                        <p>Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.</p>
                    </div>
                </div>
                <div class="mt-3">
                    <h6>Form Test</h6>
                    <form>
                        <div class="mb-3">
                            <label for="testInput" class="form-label">Test Input</label>
                            <input type="text" class="form-control" id="testInput" placeholder="Test input field">
                        </div>
                        <div class="mb-3">
                            <label for="testSelect" class="form-label">Test Select</label>
                            <select class="form-select" id="testSelect">
                                <option>Option 1</option>
                                <option>Option 2</option>
                                <option>Option 3</option>
                            </select>
                        </div>
                    </form>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <button type="button" class="btn btn-primary">Save Changes</button>
            </div>
        </div>
    </div>
</div>

<script>
function openNestedModal() {
    // First open Modal 1
    const modal1 = new bootstrap.Modal(document.getElementById('testModal1'), {
        backdrop: false,
        keyboard: true
    });
    modal1.show();

    // Then open Modal 2 after a short delay
    setTimeout(() => {
        const modal2 = new bootstrap.Modal(document.getElementById('testModal2'), {
            backdrop: false,
            keyboard: true
        });
        modal2.show();
    }, 500);
}

function openLargeModal() {
    const modal = new bootstrap.Modal(document.getElementById('largeModal'), {
        backdrop: false,
        keyboard: true
    });
    modal.show();
}

function testModalManager() {
    if (window.modalManager) {
        const count = window.modalManager.getActiveModalCount();
        const highestZ = window.modalManager.getHighestZIndex();
        
        alert(`Modal Manager Status:\nActive Modals: ${count}\nHighest Z-Index: ${highestZ}`);
        
        // Force cleanup test
        window.modalManager.forceCleanup();
        console.log('Modal manager cleanup test completed');
    } else {
        alert('Modal Manager not found! Check console for errors.');
    }
}

// Log modal events for debugging
document.addEventListener('show.bs.modal', function(e) {
    console.log('Modal showing:', e.target.id, 'Z-index:', e.target.style.zIndex);
});

document.addEventListener('shown.bs.modal', function(e) {
    console.log('Modal shown:', e.target.id);
});

document.addEventListener('hide.bs.modal', function(e) {
    console.log('Modal hiding:', e.target.id);
});

document.addEventListener('hidden.bs.modal', function(e) {
    console.log('Modal hidden:', e.target.id);
});
</script>

<?php include 'includes/footer.php'; ?>
