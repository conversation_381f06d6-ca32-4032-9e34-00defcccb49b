<?php
require_once 'config.php';

echo "<h1>Testing All Birthday Email Systems</h1>";

// Get a test member
$query = "SELECT * FROM members WHERE id = 52 LIMIT 1"; // <PERSON>
$stmt = $pdo->prepare($query);
$stmt->execute();
$member = $stmt->fetch(PDO::FETCH_ASSOC);

if (!$member) {
    echo "<p>Test member not found!</p>";
    exit;
}

echo "<h2>Test Member: " . htmlspecialchars($member['full_name']) . "</h2>";

// Get a birthday template
$query = "SELECT * FROM email_templates WHERE is_birthday_template = 1 LIMIT 1";
$stmt = $pdo->prepare($query);
$stmt->execute();
$template = $stmt->fetch(PDO::FETCH_ASSOC);

if (!$template) {
    echo "<p>No birthday template found!</p>";
    exit;
}

echo "<h2>Using Template: " . htmlspecialchars($template['template_name']) . "</h2>";

// Test 1: BirthdayReminder class (send_birthday_reminders.php)
echo "<h3>1. Testing BirthdayReminder class (send_birthday_reminders.php)</h3>";
try {
    require_once 'send_birthday_reminders.php';
    $reminderSystem = new BirthdayReminder($pdo);
    
    // Use reflection to test processTemplate method
    $reflection = new ReflectionClass($reminderSystem);
    $processTemplateMethod = $reflection->getMethod('processTemplate');
    $processTemplateMethod->setAccessible(true);
    
    $processedContent = $processTemplateMethod->invoke($reminderSystem, $template['content'], $member, 0);
    $placeholderExists = strpos($processedContent, '{birthday_member_image_url}') !== false;
    
    echo "<p><strong>Result:</strong> " . ($placeholderExists ? 
        '<span style="color: red;">FAILED - Placeholder still exists</span>' : 
        '<span style="color: green;">SUCCESS - Placeholder replaced</span>') . "</p>";
        
} catch (Exception $e) {
    echo "<p><strong>Error:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
}

// Test 2: classes/BirthdayReminder.php (using existing instance)
echo "<h3>2. Testing classes/BirthdayReminder.php</h3>";
try {
    // Use the existing BirthdayReminder instance from send_birthday_reminders.php
    if (isset($reminderSystem) && method_exists($reminderSystem, 'prepareMemberDataWithImage')) {
        // Use reflection to access the prepareMemberDataWithImage method
        $reflection = new ReflectionClass($reminderSystem);
        $prepareMethod = $reflection->getMethod('prepareMemberDataWithImage');
        $prepareMethod->setAccessible(true);

        $memberData = $prepareMethod->invoke($reminderSystem, $member);
        $hasImageUrl = isset($memberData['birthday_member_image_url']) && !empty($memberData['birthday_member_image_url']);

        echo "<p><strong>Result:</strong> " . ($hasImageUrl ?
            '<span style="color: green;">SUCCESS - birthday_member_image_url set to: ' . htmlspecialchars($memberData['birthday_member_image_url']) . '</span>' :
            '<span style="color: red;">FAILED - birthday_member_image_url not set</span>') . "</p>";
    } else {
        echo "<p><strong>Result:</strong> <span style='color: orange;'>SKIPPED - Method not available in current instance</span></p>";
    }

} catch (Exception $e) {
    echo "<p><strong>Error:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
}

// Test 3: classes/UserBirthdayManager.php
echo "<h3>3. Testing classes/UserBirthdayManager.php</h3>";
try {
    require_once 'classes/UserBirthdayManager.php';
    
    // Create a mock template for testing
    $mockTemplate = [
        'template_content' => 'Happy Birthday {birthday_member_name}! <img src="{birthday_member_image_url}" alt="{birthday_member_full_name}" />'
    ];
    
    // Test the placeholder replacement logic by simulating the fixed code
    $memberImageUrl = '';
    if (!empty($member['image_path'])) {
        $imagePath = $member['image_path'];
        if (filter_var($imagePath, FILTER_VALIDATE_URL)) {
            $memberImageUrl = $imagePath;
        } else {
            $siteUrl = defined('SITE_URL') ? SITE_URL :
                ((isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? "https" : "http") . "://" . $_SERVER['HTTP_HOST']);
            $memberImageUrl = $siteUrl . '/' . ltrim($imagePath, '/');
        }
    } else {
        $siteUrl = defined('SITE_URL') ? SITE_URL :
            ((isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? "https" : "http") . "://" . $_SERVER['HTTP_HOST']);
        $memberImageUrl = $siteUrl . '/assets/img/default-avatar.png';
    }
    
    $memberData = [
        'birthday_member_name' => $member['first_name'],
        'birthday_member_full_name' => $member['full_name'],
        'birthday_member_image_url' => $memberImageUrl,
    ];
    
    $processedContent = replaceTemplatePlaceholders($mockTemplate['template_content'], $memberData);
    $placeholderExists = strpos($processedContent, '{birthday_member_image_url}') !== false;
    
    echo "<p><strong>Result:</strong> " . ($placeholderExists ? 
        '<span style="color: red;">FAILED - Placeholder still exists</span>' : 
        '<span style="color: green;">SUCCESS - Placeholder replaced</span>') . "</p>";
    echo "<p><strong>Processed content:</strong> " . htmlspecialchars(substr($processedContent, 0, 200)) . "</p>";
        
} catch (Exception $e) {
    echo "<p><strong>Error:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
}

// Test 4: admin/send_birthday.php logic
echo "<h3>4. Testing admin/send_birthday.php logic</h3>";
try {
    // Simulate the fixed admin logic
    $memberData = $member;
    
    // Prepare member image URL (simulating the fixed code)
    $memberImageUrl = '';
    if (!empty($member['image_path'])) {
        $imagePath = $member['image_path'];
        if (filter_var($imagePath, FILTER_VALIDATE_URL)) {
            $memberImageUrl = $imagePath;
        } else {
            $siteUrl = defined('SITE_URL') ? SITE_URL :
                ((isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? "https" : "http") . "://" . $_SERVER['HTTP_HOST']);
            $memberImageUrl = $siteUrl . '/' . ltrim($imagePath, '/');
        }
    } else {
        $siteUrl = defined('SITE_URL') ? SITE_URL :
            ((isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? "https" : "http") . "://" . $_SERVER['HTTP_HOST']);
        $memberImageUrl = $siteUrl . '/assets/img/default-avatar.png';
    }
    
    // Add the fixed placeholders
    $memberData['birthday_member_image_url'] = $memberImageUrl;
    $memberData['birthday_member_image'] = $memberImageUrl;
    $memberData['birthday_member_photo_url'] = $memberImageUrl;
    
    $processedContent = replaceTemplatePlaceholders($template['content'], $memberData);
    $placeholderExists = strpos($processedContent, '{birthday_member_image_url}') !== false;
    
    echo "<p><strong>Result:</strong> " . ($placeholderExists ? 
        '<span style="color: red;">FAILED - Placeholder still exists</span>' : 
        '<span style="color: green;">SUCCESS - Placeholder replaced</span>') . "</p>";
        
} catch (Exception $e) {
    echo "<p><strong>Error:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
}

echo "<h2>Summary</h2>";
echo "<p>All birthday email systems have been tested. If all tests show SUCCESS, then the nested &lt;img&gt; tags issue should be resolved.</p>";
echo "<p><a href='logs/email_debug.log' target='_blank'>Check Email Debug Log</a></p>";
?>
