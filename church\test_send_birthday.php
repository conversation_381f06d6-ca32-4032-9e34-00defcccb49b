<?php
require_once 'config.php';
require_once 'send_birthday_reminders.php';

echo "<h2>Testing Birthday Email Sending</h2>";

// Get a member to test with
$query = "SELECT * FROM members WHERE id = 52 LIMIT 1"; // <PERSON>
$stmt = $pdo->prepare($query);
$stmt->execute();
$member = $stmt->fetch(PDO::FETCH_ASSOC);

if (!$member) {
    echo "<p>Member not found!</p>";
    exit;
}

echo "<h3>Testing Member: " . htmlspecialchars($member['full_name']) . " (" . htmlspecialchars($member['email']) . ")</h3>";

// Get a birthday template
$query = "SELECT * FROM email_templates WHERE is_birthday_template = 1 LIMIT 1";
$stmt = $pdo->prepare($query);
$stmt->execute();
$template = $stmt->fetch(PDO::FETCH_ASSOC);

if (!$template) {
    echo "<p>No birthday template found!</p>";
    exit;
}

echo "<h3>Using Template: " . htmlspecialchars($template['template_name']) . "</h3>";

// Create an instance of the BirthdayReminder class
$reminderSystem = new BirthdayReminder($pdo);

// Test sending a birthday email
echo "<h3>Sending Birthday Email...</h3>";

try {
    // Use reflection to access the private sendEmail method
    $reflection = new ReflectionClass($reminderSystem);
    $sendEmailMethod = $reflection->getMethod('sendEmail');
    $sendEmailMethod->setAccessible(true);

    // Send the email
    $result = $sendEmailMethod->invoke($reminderSystem, $member, $template, 0, 'birthday');
    
    if ($result) {
        echo "<p style='color: green;'><strong>SUCCESS: Birthday email sent successfully!</strong></p>";
    } else {
        echo "<p style='color: red;'><strong>ERROR: Failed to send birthday email!</strong></p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'><strong>ERROR: " . htmlspecialchars($e->getMessage()) . "</strong></p>";
}

echo "<h3>Check the email debug log for details:</h3>";
echo "<p><a href='logs/email_debug.log' target='_blank'>View Email Debug Log</a></p>";
?>
