/* Admin Panel Styles - Updated for Production 2024 with Symmetric Layout */

/* CSS Custom Properties for Symmetric Padding */
:root {
    --admin-content-padding-desktop: 30px;
    --admin-content-padding-tablet: 25px;
    --admin-content-padding-mobile: 20px;
    --admin-content-padding-small: 15px;
    --admin-vertical-spacing: 20px;
}

/* Debug mode - Uncomment to visualize padding areas */
/*
.main-content {
    background: linear-gradient(90deg,
        rgba(255,0,0,0.3) 0,
        rgba(255,0,0,0.3) var(--admin-content-padding-desktop),
        transparent var(--admin-content-padding-desktop),
        transparent calc(100% - var(--admin-content-padding-desktop)),
        rgba(255,0,0,0.3) calc(100% - var(--admin-content-padding-desktop)),
        rgba(255,0,0,0.3) 100%) !important;
}

.main-content::before {
    content: "Left: " var(--admin-content-padding-desktop) " | Right: " var(--admin-content-padding-desktop);
    position: fixed;
    top: 10px;
    right: 10px;
    background: rgba(0,0,0,0.8);
    color: white;
    padding: 5px 10px;
    border-radius: 3px;
    font-size: 12px;
    z-index: 9999;
}
*/

/* Visual verification - Add temporary border to see padding */
/*
.main-content {
    border-left: 2px solid red !important;
    border-right: 2px solid red !important;
}
*/

/* Global fixes for path issues and layout */
html, body {
    margin: 0;
    padding: 0;
    height: 100%;
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif;
    overflow-x: hidden;
}

/* Ensure all elements use border-box sizing */
*, *::before, *::after {
    box-sizing: border-box;
}

/* Important: Fix layout for all environments */
.container-fluid {
    padding-left: 0;
    padding-right: 0;
    width: 100%;
    max-width: 100%;
    overflow-x: hidden;
}

/* Prevent content overflow in admin pages */
.main-content .container-fluid,
.main-content .container,
.main-content .row {
    max-width: 100%;
    overflow-x: hidden;
}

/* Ensure tables and wide content don't overflow */
.main-content .table-responsive {
    margin-right: 0;
    padding-right: 0;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
}

/* Prevent wide content from breaking layout */
.main-content * {
    max-width: 100%;
    word-wrap: break-word;
    overflow-wrap: break-word;
}

/* Fix for wide forms and inputs */
.main-content .form-control,
.main-content .form-select,
.main-content .btn,
.main-content input,
.main-content textarea,
.main-content select {
    max-width: 100%;
    box-sizing: border-box;
}

/* Ensure cards don't overflow */
.main-content .card {
    max-width: 100%;
    overflow: hidden;
    word-wrap: break-word;
}

/* Fix for wide tables */
.main-content table {
    table-layout: auto;
    width: 100%;
    max-width: 100%;
}

/* Prevent pre and code blocks from overflowing */
.main-content pre,
.main-content code {
    max-width: 100%;
    overflow-x: auto;
    word-wrap: break-word;
    white-space: pre-wrap;
}

/* Fix for Bootstrap columns */
.main-content .col,
.main-content [class*="col-"] {
    max-width: 100%;
    overflow-wrap: break-word;
}

/* Ensure buttons don't cause overflow */
.main-content .btn-group,
.main-content .btn-toolbar {
    flex-wrap: wrap;
}

/* Fix for wide alerts and notifications */
.main-content .alert {
    max-width: 100%;
    word-wrap: break-word;
}
}

.main-content .card,
.main-content .form-group,
.main-content .input-group {
    max-width: 100%;
    box-sizing: border-box;
}

/* Bootstrap grid system fixes for admin pages */
.main-content .row {
    margin-right: 0;
    margin-left: 0;
}

.main-content .col,
.main-content [class*="col-"] {
    padding-right: 15px;
    padding-left: 15px;
    max-width: 100%;
    box-sizing: border-box;
}

/* Ensure buttons and action groups don't overflow */
.main-content .btn-toolbar,
.main-content .btn-group {
    flex-wrap: wrap;
}

/* Fix for wide tables */
.main-content .table {
    table-layout: auto;
    word-wrap: break-word;
}

/* Ensure form elements don't overflow */
.main-content .form-control,
.main-content .form-select {
    max-width: 100%;
    box-sizing: border-box;
}

/* Fix for action buttons in tables */
.main-content .table td:last-child,
.main-content .table th:last-child {
    white-space: nowrap;
    min-width: 120px;
}

.main-content .table .btn-group {
    display: flex;
    flex-wrap: nowrap;
    gap: 2px;
}

.main-content .table .btn-sm {
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
}

/* Ensure table responsive containers don't overflow */
.main-content .table-responsive {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
}

/* Fix for page headers with action buttons */
.main-content .d-flex.justify-content-between {
    flex-wrap: wrap;
    gap: 1rem;
}

.main-content .btn-toolbar {
    flex-wrap: wrap;
    gap: 0.5rem;
}

/* Fix for statistics cards and content cards */
.main-content .card {
    margin-bottom: 1rem;
    word-wrap: break-word;
}

.main-content .card-body {
    padding: 1rem;
}

/* Ensure alert messages don't overflow */
.main-content .alert {
    word-wrap: break-word;
    overflow-wrap: break-word;
}

/* Fix for modal content */
.modal-body .table-responsive {
    max-height: 400px;
    overflow-y: auto;
}

/* Ensure breadcrumbs and navigation don't overflow */
.main-content .breadcrumb {
    flex-wrap: wrap;
}

/* Fix for form rows in modals */
.modal-body .row {
    margin-right: 0;
    margin-left: 0;
}

.modal-body .col,
.modal-body [class*="col-"] {
    padding-right: 0.75rem;
    padding-left: 0.75rem;
}

/* Ensure symmetric layout for page headers */
.main-content .d-flex.justify-content-between.align-items-center {
    margin-bottom: 1.5rem;
}

/* Consistent spacing for admin page sections */
.main-content .card {
    margin-bottom: 1.5rem;
}

.main-content .row {
    margin-bottom: 1rem;
}

/* Ensure content doesn't touch the edges with symmetric padding */
.main-content > .container-fluid {
    padding-left: 0;
    padding-right: 0;
    /* Note: Main content padding is handled by .main-content itself */
}

/* Balance the visual weight of content */
.main-content .page-header {
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid #dee2e6;
}

/* Symmetric layout adjustments for common admin page elements */
.main-content .d-flex.justify-content-between.flex-wrap.flex-md-nowrap.align-items-center {
    margin-bottom: 1.5rem;
    padding-bottom: 0.75rem;
    border-bottom: 1px solid #e9ecef;
}

/* Ensure statistics cards have balanced spacing */
.main-content .row.mb-4 {
    margin-bottom: 2rem !important;
}

/* Balance spacing for filter forms */
.main-content .card .card-body form.row {
    margin-bottom: 0;
}

/* Consistent spacing for admin tables */
.main-content .table-responsive {
    margin-bottom: 1.5rem;
}

/* Ensure action buttons are properly spaced */
.main-content .btn-toolbar.mb-2.mb-md-0 {
    margin-bottom: 1rem !important;
}

/* Visual balance for admin page titles */
.main-content h1.h2,
.main-content h2 {
    margin-bottom: 1rem;
    font-weight: 600;
}

/* Consistent card spacing throughout admin pages */
.main-content .card + .card {
    margin-top: 1.5rem;
}

/* Force symmetric padding - Override any conflicting styles */
.main-content,
.container-fluid.main-content {
    /* Ensure symmetric padding is always applied */
    box-sizing: border-box !important;
}

/* Prevent any child elements from overriding the main content padding */
.main-content > .container-fluid {
    padding-left: 0 !important;
    padding-right: 0 !important;
    margin-left: 0 !important;
    margin-right: 0 !important;
    max-width: 100% !important;
}

/* Ensure Bootstrap row doesn't add negative margins that break layout */
.main-content > .row,
.main-content .container-fluid > .row {
    margin-left: 0 !important;
    margin-right: 0 !important;
}

/* High specificity rule to ensure symmetric padding is always applied */
body .main-content,
body .container-fluid.main-content {
    /* Force symmetric padding with high specificity */
    padding-left: var(--admin-content-padding-desktop) !important;
    padding-right: var(--admin-content-padding-desktop) !important;
}

/* Responsive symmetric padding with high specificity */
@media (max-width: 1366px) {
    body .main-content,
    body .container-fluid.main-content {
        padding-left: var(--admin-content-padding-tablet) !important;
        padding-right: var(--admin-content-padding-tablet) !important;
    }
}

@media (max-width: 768px) {
    body .main-content,
    body .container-fluid.main-content {
        padding-left: var(--admin-content-padding-mobile) !important;
        padding-right: var(--admin-content-padding-mobile) !important;
    }
}

@media (max-width: 480px) {
    body .main-content,
    body .container-fluid.main-content {
        padding-left: var(--admin-content-padding-small) !important;
        padding-right: var(--admin-content-padding-small) !important;
    }
}

/* Simple Sidebar Styles with Theme Support */
.sidebar {
    background-color: var(--sidebar-bg-color, #343a40);
    color: var(--sidebar-text-color, white);
    padding: 0;
    position: fixed !important;
    width: var(--sidebar-width, 280px);
    z-index: 100;
    overflow-y: auto !important;
    height: 100vh !important;
    max-height: 100vh !important;
    top: 0 !important;
    left: 0 !important;
    transition: width 0.3s ease, background-color 0.3s ease;
}

/* Sidebar Header */
.sidebar .sidebar-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 15px 20px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    min-height: 70px;
    background-color: rgba(0, 0, 0, 0.1);
}

.sidebar .logo-container {
    display: flex;
    align-items: center;
    flex: 1;
}

.sidebar .sidebar-logo-text {
    display: block;
}

.sidebar .sidebar-initials-container {
    display: none;
    justify-content: center;
    align-items: center;
}

.sidebar .sidebar-initials {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: linear-gradient(135deg, #007bff, #0056b3);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 14px;
    letter-spacing: 1px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    border: 2px solid rgba(255,255,255,0.2);
}

.sidebar .sidebar-toggle-btn {
    background: rgba(255, 255, 255, 0.1);
    border: none;
    color: var(--sidebar-text-color, white);
    padding: 8px;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.sidebar .sidebar-toggle-btn:hover {
    background: rgba(255, 255, 255, 0.2);
}

.sidebar.collapsed {
    width: 60px !important;
    min-width: 60px !important;
    overflow: hidden !important;
    background-color: var(--sidebar-bg-color, #343a40) !important;
}

.sidebar.collapsed .nav-item a {
    border-radius: 8px;
    margin: 2px 5px;
    transition: all 0.2s ease;
}

.sidebar.collapsed .nav-item a:hover {
    background-color: rgba(255, 255, 255, 0.1) !important;
    transform: scale(1.05);
}

/* Sidebar Content - Main scrolling container */
.sidebar .sidebar-content {
    padding: 0;
    overflow-y: auto !important;
    overflow-x: hidden !important;
    max-height: calc(100vh - 70px) !important; /* Account for header height */
    height: calc(100vh - 70px) !important;
    scroll-behavior: smooth !important;
    position: relative;
}

/* Smooth scrolling for collapsed sidebar */
.sidebar.collapsed .sidebar-content {
    overflow-y: auto !important;
    overflow-x: hidden !important;
    max-height: calc(100vh - 70px) !important; /* Same height for consistency */
    height: calc(100vh - 70px) !important;
    scroll-behavior: smooth !important;
}

/* Hide scrollbar while maintaining scrolling functionality */
.sidebar .sidebar-content::-webkit-scrollbar {
    display: none; /* Hide scrollbar for Chrome, Safari and Opera */
}

.sidebar .sidebar-content {
    -ms-overflow-style: none;  /* Hide scrollbar for IE and Edge */
    scrollbar-width: none;  /* Hide scrollbar for Firefox */
}

/* Ensure sidebar content can scroll properly */
.sidebar {
    display: flex;
    flex-direction: column;
}

.sidebar .sidebar-content {
    flex: 1;
    min-height: 0; /* Important for flexbox scrolling */
}

/* Smooth scrolling for all sidebar states */
.sidebar,
.sidebar.collapsed {
    scroll-behavior: smooth;
}

/* Ensure nav items don't interfere with scrolling */
.sidebar .nav-item {
    flex-shrink: 0;
    overflow: visible;
    position: relative;
}



/* Ensure the sidebar has proper height constraints */
.sidebar {
    max-height: 100vh !important;
    height: 100vh !important;
}

/* Debug: Add border to see the scrollable area */
.sidebar .sidebar-content {
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
    box-sizing: border-box;
}

.sidebar .nav {
    padding: 0;
    margin: 0;
    list-style: none;
    height: auto;
    overflow: visible;
    flex-direction: column;
}

.sidebar .nav-item {
    margin: 0;
}

.sidebar .nav-section-header {
    margin-top: 20px;
}

.sidebar .nav-section-header:first-child {
    margin-top: 10px;
}

.sidebar .nav-section-title {
    display: flex;
    align-items: center;
    padding: 8px 20px;
    font-size: 0.85rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    color: rgba(255, 255, 255, 0.7);
    background-color: rgba(0, 0, 0, 0.1);
}

.sidebar .nav-link {
    display: flex;
    align-items: center;
    padding: 12px 20px;
    color: var(--sidebar-text-color, white);
    text-decoration: none;
    transition: all 0.3s ease;
    border: none;
    background: none;
}

.sidebar .nav-link:hover {
    background-color: var(--sidebar-hover-color, rgba(255, 255, 255, 0.1));
    color: var(--sidebar-text-color, white);
    text-decoration: none;
}

.sidebar .nav-link.active {
    background-color: var(--sidebar-hover-color, rgba(255, 255, 255, 0.2));
    color: var(--sidebar-text-color, white);
    border-left: 3px solid var(--primary-color, #007bff);
}

.sidebar .nav-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 24px;
    margin-right: 12px;
    flex-shrink: 0;
}

.sidebar .nav-icon svg {
    width: 18px;
    height: 18px;
}

.sidebar .menu-text {
    flex: 1;
    font-size: 0.9rem;
    font-weight: 500;
}

/* Main Content Styles with Theme Support - Symmetric Padding */
.main-content {
    /* Explicit symmetric padding for visual balance */
    padding-top: var(--admin-vertical-spacing);
    padding-bottom: var(--admin-vertical-spacing);
    padding-left: var(--admin-content-padding-desktop);
    padding-right: var(--admin-content-padding-desktop);
    margin-left: var(--sidebar-width, 280px);
    margin-right: var(--admin-content-padding-desktop);
    transition: margin-left 0.3s ease, width 0.3s ease, margin-right 0.3s ease;
    width: calc(100% - var(--sidebar-width, 280px) - var(--admin-content-padding-desktop));
    position: relative;
    z-index: 1;
    overflow-x: hidden;
    overflow-y: auto;
    min-height: 100vh;
    background-color: #f8f9fa;
    box-sizing: border-box;
    scroll-behavior: smooth;
}

/* Ensure the calc function is properly recognized - Symmetric Padding */
.container-fluid.main-content {
    margin-left: var(--sidebar-width, 280px) !important;
    margin-right: var(--admin-content-padding-desktop) !important;
    width: calc(100% - var(--sidebar-width, 280px) - var(--admin-content-padding-desktop)) !important;
    /* Explicit symmetric padding for container-fluid */
    padding-top: var(--admin-vertical-spacing) !important;
    padding-bottom: var(--admin-vertical-spacing) !important;
    padding-left: var(--admin-content-padding-desktop) !important;
    padding-right: var(--admin-content-padding-desktop) !important;
    overflow-x: hidden !important;
    box-sizing: border-box !important;
}

.main-content.expanded {
    margin-left: 60px !important;
    margin-right: var(--admin-content-padding-desktop) !important;
    width: calc(100% - 60px - var(--admin-content-padding-desktop)) !important;
    /* Explicit symmetric padding when sidebar is collapsed */
    padding-top: var(--admin-vertical-spacing) !important;
    padding-bottom: var(--admin-vertical-spacing) !important;
    padding-left: var(--admin-content-padding-desktop) !important;
    padding-right: var(--admin-content-padding-desktop) !important;
    overflow-x: hidden !important;
}

/* Collapsed Sidebar Styles */
.sidebar.collapsed {
    width: 60px !important;
    min-width: 60px !important;
}

.sidebar.collapsed .sidebar-header {
    padding: 15px 5px;
    justify-content: center;
}

.sidebar.collapsed .sidebar-logo-text {
    display: none !important;
}

.sidebar.collapsed .sidebar-initials-container {
    display: flex !important;
    justify-content: center;
    width: 100%;
}

.sidebar.collapsed .sidebar-toggle-btn {
    position: absolute;
    right: -15px;
    top: 50%;
    transform: translateY(-50%);
    background: var(--sidebar-bg-color, #343a40);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    width: 30px;
    height: 30px;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
}

.sidebar.collapsed .nav-section-title {
    display: none !important;
}

.sidebar.collapsed .nav-section-header {
    margin: 5px 0;
    height: 1px;
    background: rgba(255, 255, 255, 0.1);
}

.sidebar.collapsed .nav-link {
    padding: 12px 0;
    justify-content: center;
    position: relative;
    display: flex;
    align-items: center;
}

.sidebar.collapsed .nav-icon {
    margin-right: 0 !important;
    width: 24px;
    display: flex;
    justify-content: center;
    align-items: center;
}

.sidebar.collapsed .menu-text {
    display: none !important;
}

/* Hide any text in collapsed sidebar */
.sidebar.collapsed .nav-item button {
    padding: 12px 0;
    justify-content: center;
    min-width: auto;
}

.sidebar.collapsed .nav-item button .menu-text,
.sidebar.collapsed .nav-item button span:not(.nav-icon) {
    display: none !important;
}

/* Ensure language selector is properly centered */
.sidebar.collapsed .nav-item button {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
}

/* Tooltip for collapsed sidebar */
.sidebar.collapsed .nav-link:hover::after {
    content: attr(title);
    position: absolute;
    left: 100%;
    top: 50%;
    transform: translateY(-50%);
    background: rgba(0, 0, 0, 0.9);
    color: white;
    padding: 8px 12px;
    border-radius: 4px;
    font-size: 0.85rem;
    white-space: nowrap;
    z-index: 1000;
    margin-left: 10px;
    pointer-events: none;
}

.sidebar.collapsed .nav-link:hover::before {
    content: '';
    position: absolute;
    left: 100%;
    top: 50%;
    transform: translateY(-50%);
    border: 5px solid transparent;
    border-right-color: rgba(0, 0, 0, 0.9);
    margin-left: 5px;
    z-index: 1000;
    pointer-events: none;
}

/* Improved sidebar collapse toggle button */
#sidebarCollapseToggle {
    position: fixed;
    bottom: 20px;
    left: 230px;
    z-index: 9999;
    background-color: #343a40;
    color: white;
    border: 1px solid rgba(255,255,255,0.2);
    border-radius: 50%;
    width: 35px;
    height: 35px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(0,0,0,0.3);
    padding: 0;
    font-size: 14px;
    pointer-events: auto;
}

#sidebarCollapseToggle:hover {
    background-color: #007bff;
    border-color: #007bff;
    transform: scale(1.1);
}

.sidebar.collapsed #sidebarCollapseToggle {
    left: 15px;
}

.sidebar.collapsed #sidebarCollapseToggle i {
    transform: rotate(180deg);
}

/* Sidebar Logo Styles */
.sidebar .logo-container {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    width: 100%;
    flex-wrap: wrap;
}

.sidebar .sidebar-logo {
    max-height: 60px;
    max-width: 220px;
    height: auto;
    width: auto;
    object-fit: contain;
    /* Remove the white filter to show original logo colors */
    /* filter: brightness(0) invert(1); */
}

.sidebar .navbar-brand-text {
    font-size: 1rem;
    font-weight: 600;
    color: var(--sidebar-text-color, white);
}

/* Sidebar Brand Container - Show/Hide Logic */
.sidebar .sidebar-brand-container {
    display: block;
}

.sidebar .sidebar-initials-container {
    display: none;
}

.sidebar.collapsed .sidebar-brand-container {
    display: none !important;
}

.sidebar.collapsed .sidebar-initials-container {
    display: flex !important;
    justify-content: center !important;
    align-items: center !important;
    width: 100% !important;
}

/* Sidebar Initials Styling */
.sidebar-initials {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: linear-gradient(135deg, #007bff, #0056b3);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 14px;
    letter-spacing: 1px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    border: 2px solid rgba(255,255,255,0.2);
}

.sidebar .sidebar-header {
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    padding: 15px;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    min-height: 70px;
    position: relative;
}

/* Enhanced Sidebar Toggle Button */
.sidebar-toggle-btn {
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    background: rgba(255,255,255,0.1);
    border: 1px solid rgba(255,255,255,0.2);
    color: white;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    z-index: 10;
}

.sidebar-toggle-btn:hover {
    background: rgba(255,255,255,0.2);
    border-color: rgba(255,255,255,0.3);
    transform: translateY(-50%) scale(1.1);
}

.sidebar.collapsed .sidebar-toggle-btn {
    right: 50%;
    transform: translateY(-50%) translateX(50%);
}

.sidebar.collapsed .sidebar-toggle-btn i {
    transform: rotate(180deg);
}

/* Sidebar Navigation Styles with Theme Support */
.sidebar .nav-link,
.sidebar a {
    color: var(--sidebar-text-color, white) !important;
    padding: 10px 15px;
    border-radius: 5px;
    margin: 2px 10px;
    transition: all 0.3s ease;
    text-decoration: none;
}

.sidebar .nav-link:hover,
.sidebar a:hover {
    background-color: var(--sidebar-hover-color, #007bff) !important;
    color: #ffffff !important;
    transform: translateX(5px);
}

.sidebar .nav-link.active,
.sidebar a.active {
    background-color: var(--sidebar-hover-color, #007bff) !important;
    color: #ffffff !important;
}

/* Ensure proper icon alignment */
.sidebar .bi,
.sidebar .fas,
.sidebar .far,
.sidebar .fab {
    width: 20px;
    text-align: center;
    display: inline-block;
    margin-right: 10px;
}

.sidebar .bi {
    font-family: 'bootstrap-icons' !important;
}

.sidebar .fas,
.sidebar .far,
.sidebar .fab {
    font-family: 'Font Awesome 6 Free', 'Font Awesome 6 Brands' !important;
}

/* Menu text and icons */
.menu-text {
    opacity: 1;
    transition: opacity 0.2s;
}

.sidebar.collapsed .menu-text {
    display: none !important;
}

.sidebar.collapsed .navbar-brand-text {
    display: none !important;
}

.sidebar.collapsed .sidebar-heading {
    display: none !important;
}

/* Collapsed Sidebar Styling */
.sidebar.collapsed {
    width: 60px !important;
    min-width: 60px !important;
    max-width: 60px !important;
    /* Allow content to scroll within the sidebar */
}

/* Category headers styling for collapsed sidebar */
.sidebar.collapsed .category-header {
    display: flex !important;
    justify-content: center !important;
    align-items: center !important;
    padding: 8px 0 !important;
    margin: 4px 0 !important;
    border-bottom: 1px solid rgba(255,255,255,0.1);
}

.sidebar.collapsed .category-header .sidebar-heading {
    display: flex !important;
    justify-content: center !important;
    align-items: center !important;
    padding: 0 !important;
    margin: 0 !important;
    width: 100% !important;
}

.sidebar.collapsed .category-header .sidebar-heading .menu-text {
    display: none !important;
}

.sidebar.collapsed .category-header .sidebar-heading i {
    display: inline-block !important;
    margin: 0 !important;
    font-size: 1.1rem !important;
    color: rgba(255,255,255,0.7) !important;
    width: auto !important;
    opacity: 1 !important;
    visibility: visible !important;
    line-height: 1 !important;
}

/* Ensure Font Awesome icons in category headers display properly */
.sidebar.collapsed .category-header .sidebar-heading .fas,
.sidebar.collapsed .category-header .sidebar-heading .far,
.sidebar.collapsed .category-header .sidebar-heading .fab,
.sidebar.collapsed .category-header .sidebar-heading .fa {
    font-family: 'Font Awesome 6 Free', 'Font Awesome 6 Brands', 'FontAwesome' !important;
    font-weight: 900 !important;
    display: inline-block !important;
    text-rendering: auto !important;
    -webkit-font-smoothing: antialiased !important;
}

/* Ensure Bootstrap Icons in category headers display properly */
.sidebar.collapsed .category-header .sidebar-heading .bi {
    font-family: 'bootstrap-icons' !important;
    display: inline-block !important;
    text-rendering: auto !important;
    -webkit-font-smoothing: antialiased !important;
}

/* Menu items in collapsed sidebar */
.sidebar.collapsed .nav-item a {
    text-align: center !important;
    padding: 10px 0 !important;
    margin: 2px 0 !important;
    display: flex !important;
    justify-content: center !important;
    align-items: center !important;
    position: relative !important;
    overflow: visible !important;
    width: 100% !important;
    min-height: 36px !important;
    border-radius: 4px !important;
}

.sidebar.collapsed .nav-item a i {
    margin: 0 !important;
    font-size: 1.2rem !important;
    width: auto !important;
    display: inline-block !important;
    text-align: center !important;
    color: inherit !important;
    opacity: 1 !important;
    visibility: visible !important;
    line-height: 1 !important;
}

/* Ensure Font Awesome icons display properly */
.sidebar.collapsed .nav-item a .fas,
.sidebar.collapsed .nav-item a .far,
.sidebar.collapsed .nav-item a .fab,
.sidebar.collapsed .nav-item a .fa {
    font-family: 'Font Awesome 6 Free', 'Font Awesome 6 Brands', 'FontAwesome' !important;
    font-weight: 900 !important;
    display: inline-block !important;
    text-rendering: auto !important;
    -webkit-font-smoothing: antialiased !important;
}

.sidebar.collapsed .nav-item a .bi {
    font-family: 'bootstrap-icons' !important;
    display: inline-block !important;
    text-rendering: auto !important;
    -webkit-font-smoothing: antialiased !important;
}

/* Hide menu text in collapsed sidebar - HIGH SPECIFICITY */
.sidebar.collapsed .nav-item a .menu-text,
.sidebar.collapsed .category-header .sidebar-heading .menu-text {
    display: none !important;
}

/* Ensure icons are visible and centered - HIGH SPECIFICITY */
.sidebar.collapsed .nav-item a i,
.sidebar.collapsed .category-header .sidebar-heading i {
    display: inline-block !important;
    margin: 0 !important;
    width: auto !important;
    text-align: center !important;
    opacity: 1 !important;
    visibility: visible !important;
    font-size: 1.2rem !important;
    line-height: 1 !important;
}

/* Force icon display for both Font Awesome and Bootstrap Icons - ULTRA HIGH SPECIFICITY */
.sidebar.collapsed .nav-item a i.fas,
.sidebar.collapsed .nav-item a i.far,
.sidebar.collapsed .nav-item a i.fab,
.sidebar.collapsed .nav-item a i.fa,
.sidebar.collapsed .nav-item a i.bi,
.sidebar.collapsed .category-header .sidebar-heading i.fas,
.sidebar.collapsed .category-header .sidebar-heading i.far,
.sidebar.collapsed .category-header .sidebar-heading i.fab,
.sidebar.collapsed .category-header .sidebar-heading i.fa,
.sidebar.collapsed .category-header .sidebar-heading i.bi {
    display: inline-block !important;
    opacity: 1 !important;
    visibility: visible !important;
    font-size: 1.2rem !important;
    width: auto !important;
    height: auto !important;
    margin: 0 !important;
    padding: 0 !important;
    text-align: center !important;
    vertical-align: middle !important;
    line-height: 1 !important;
}

/* Simple and clean collapsed sidebar text hiding */
.sidebar.collapsed .nav-item a .menu-text,
.sidebar.collapsed .category-header .sidebar-heading .menu-text {
    display: none !important;
}

/* COMPREHENSIVE ICON VISIBILITY - Ensure ALL icons are visible in collapsed sidebar */
.sidebar.collapsed .nav-item a i,
.sidebar.collapsed .category-header .sidebar-heading i,
.sidebar.collapsed .nav-item a .fas,
.sidebar.collapsed .nav-item a .far,
.sidebar.collapsed .nav-item a .fab,
.sidebar.collapsed .nav-item a .fa,
.sidebar.collapsed .nav-item a .bi,
.sidebar.collapsed .category-header .sidebar-heading .fas,
.sidebar.collapsed .category-header .sidebar-heading .far,
.sidebar.collapsed .category-header .sidebar-heading .fab,
.sidebar.collapsed .category-header .sidebar-heading .fa,
.sidebar.collapsed .category-header .sidebar-heading .bi {
    display: inline-block !important;
    font-size: 1.2rem !important;
    margin: 0 auto !important;
    opacity: 1 !important;
    visibility: visible !important;
    width: auto !important;
    height: auto !important;
    text-align: center !important;
    line-height: 1 !important;
    font-weight: normal !important;
    font-style: normal !important;
    text-decoration: none !important;
    vertical-align: middle !important;
}

/* Hide all text in collapsed mode */
.sidebar.collapsed .menu-text {
    display: none !important;
}

/* FORCE ICON FONTS TO LOAD PROPERLY */
.sidebar.collapsed .nav-item a .bi::before,
.sidebar.collapsed .category-header .sidebar-heading .bi::before {
    font-family: 'bootstrap-icons' !important;
    display: inline-block !important;
    font-style: normal !important;
    font-variant: normal !important;
    text-rendering: auto !important;
    line-height: 1 !important;
}

.sidebar.collapsed .nav-item a .fas::before,
.sidebar.collapsed .nav-item a .far::before,
.sidebar.collapsed .nav-item a .fab::before,
.sidebar.collapsed .nav-item a .fa::before,
.sidebar.collapsed .category-header .sidebar-heading .fas::before,
.sidebar.collapsed .category-header .sidebar-heading .far::before,
.sidebar.collapsed .category-header .sidebar-heading .fab::before,
.sidebar.collapsed .category-header .sidebar-heading .fa::before {
    font-family: 'Font Awesome 6 Free', 'Font Awesome 5 Free', 'FontAwesome' !important;
    display: inline-block !important;
    font-style: normal !important;
    font-variant: normal !important;
    text-rendering: auto !important;
    line-height: 1 !important;
    font-weight: 900 !important;
}

/* MENU ITEM LAYOUT IN COLLAPSED MODE */
.sidebar.collapsed .nav-item a {
    display: flex !important;
    justify-content: center !important;
    align-items: center !important;
    padding: 10px 0 !important;
    text-align: center !important;
    min-height: 40px !important;
}

.sidebar.collapsed .category-header .sidebar-heading {
    display: flex !important;
    justify-content: center !important;
    align-items: center !important;
    padding: 8px 0 !important;
    text-align: center !important;
}
    display: inline-block !important;
    content: attr(data-icon) !important;
}

/* Specific icon fixes for collapsed sidebar */
.sidebar.collapsed .bi-speedometer2::before { content: "\f4c4" !important; }
.sidebar.collapsed .bi-people::before { content: "\f4d6" !important; }
.sidebar.collapsed .bi-person-plus::before { content: "\f4da" !important; }
.sidebar.collapsed .bi-calendar-event::before { content: "\f1d3" !important; }
.sidebar.collapsed .bi-clipboard-check::before { content: "\f26b" !important; }
.sidebar.collapsed .bi-tags::before { content: "\f5d2" !important; }
.sidebar.collapsed .bi-bar-chart::before { content: "\f1bc" !important; }
.sidebar.collapsed .bi-envelope::before { content: "\f32f" !important; }
.sidebar.collapsed .bi-clock::before { content: "\f292" !important; }
.sidebar.collapsed .bi-person-lines-fill::before { content: "\f4dd" !important; }
.sidebar.collapsed .bi-collection::before { content: "\f2a6" !important; }
.sidebar.collapsed .bi-gift::before { content: "\f3a5" !important; }
.sidebar.collapsed .bi-send::before { content: "\f52c" !important; }
.sidebar.collapsed .bi-bug::before { content: "\f1c6" !important; }
.sidebar.collapsed .bi-bell::before { content: "\f1f6" !important; }
.sidebar.collapsed .bi-file-earmark-text::before { content: "\f32a" !important; }
.sidebar.collapsed .bi-robot::before { content: "\f4f8" !important; }
.sidebar.collapsed .bi-whatsapp::before { content: "\f5f5" !important; }
.sidebar.collapsed .bi-chat-dots::before { content: "\f259" !important; }
.sidebar.collapsed .bi-graph-up::before { content: "\f3b1" !important; }
.sidebar.collapsed .bi-info-circle::before { content: "\f3f1" !important; }
.sidebar.collapsed .bi-chat-text::before { content: "\f25c" !important; }
.sidebar.collapsed .bi-broadcast::before { content: "\f1c4" !important; }
.sidebar.collapsed .bi-telephone::before { content: "\f5d4" !important; }
.sidebar.collapsed .bi-calendar3::before { content: "\f1d1" !important; }
.sidebar.collapsed .bi-share::before { content: "\f52d" !important; }
.sidebar.collapsed .bi-credit-card::before { content: "\f2b4" !important; }
.sidebar.collapsed .bi-table::before { content: "\f5ce" !important; }
.sidebar.collapsed .bi-heart::before { content: "\f3c7" !important; }
.sidebar.collapsed .bi-shield-check::before { content: "\f538" !important; }
.sidebar.collapsed .bi-gear::before { content: "\f3a2" !important; }
.sidebar.collapsed .bi-palette::before { content: "\f4a8" !important; }
.sidebar.collapsed .bi-input-cursor-text::before { content: "\f3f8" !important; }
.sidebar.collapsed .bi-image::before { content: "\f3ee" !important; }
.sidebar.collapsed .bi-brush::before { content: "\f1c5" !important; }
.sidebar.collapsed .bi-shield-lock::before { content: "\f53a" !important; }
.sidebar.collapsed .bi-shield-exclamation::before { content: "\f539" !important; }
.sidebar.collapsed .bi-person::before { content: "\f4da" !important; }
.sidebar.collapsed .bi-box-arrow-right::before { content: "\f1c0" !important; }

/* Ensure no content overflows except for the scrollable content */
.sidebar.collapsed .nav-link {
    overflow: hidden !important;
    white-space: nowrap !important;
}

.sidebar.collapsed .menu-text {
    overflow: hidden !important;
    white-space: nowrap !important;
}

/* For tablet and small laptops */
@media (max-width: 1366px) {
    .sidebar {
        width: 250px;
    }

    .main-content,
    .container-fluid.main-content {
        margin-left: 250px !important;
        margin-right: var(--admin-content-padding-tablet) !important;
        width: calc(100% - 250px - var(--admin-content-padding-tablet)) !important;
        max-width: calc(100% - 250px - var(--admin-content-padding-tablet)) !important;
        /* Explicit symmetric padding for tablet layout */
        padding-top: var(--admin-vertical-spacing) !important;
        padding-bottom: var(--admin-vertical-spacing) !important;
        padding-left: var(--admin-content-padding-tablet) !important;
        padding-right: var(--admin-content-padding-tablet) !important;
        overflow-x: hidden !important;
        box-sizing: border-box !important;
    }
    
    .sidebar.collapsed {
        width: 50px !important;
        min-width: 50px !important;
    }
    
    .main-content.expanded {
        margin-left: 50px !important;
        margin-right: var(--admin-content-padding-tablet) !important;
        width: calc(100% - 50px - var(--admin-content-padding-tablet)) !important;
        /* Explicit symmetric padding for tablet layout when collapsed */
        padding-top: var(--admin-vertical-spacing) !important;
        padding-bottom: var(--admin-vertical-spacing) !important;
        padding-left: var(--admin-content-padding-tablet) !important;
        padding-right: var(--admin-content-padding-tablet) !important;
    }
    
    #sidebarCollapseToggle {
        left: 180px;
    }
    
    .sidebar a {
        font-size: 0.85rem;
        padding: 6px 10px;
    }
}

/* Mobile styles - improved */
@media (max-width: 768px) {
    .sidebar {
        width: 100% !important;
        position: relative !important;
        height: auto !important;
        max-height: none !important;
        overflow-y: visible !important;
        padding-bottom: 10px;
        min-height: auto !important;
    }
    
    .main-content,
    .container-fluid.main-content {
        margin-left: 0 !important;
        margin-right: var(--admin-content-padding-mobile) !important;
        width: calc(100% - var(--admin-content-padding-mobile)) !important;
        max-width: calc(100% - var(--admin-content-padding-mobile)) !important;
        /* Explicit symmetric padding for mobile layout */
        padding-top: var(--admin-vertical-spacing) !important;
        padding-bottom: var(--admin-vertical-spacing) !important;
        padding-left: var(--admin-content-padding-mobile) !important;
        padding-right: var(--admin-content-padding-mobile) !important;
        overflow-x: hidden !important;
        box-sizing: border-box !important;
    }
    
    .sidebar.collapsed {
        height: auto !important;
    }
    
    .sidebar-collapsed .sidebar-content {
        display: none;
    }
    
    #sidebarCollapseToggle {
        display: none !important;
    }
    
    /* Make mobile toggle button more visible */
    #sidebarToggle {
        display: block !important;
        padding: 5px 10px;
        background-color: #343a40;
        border: 1px solid rgba(255,255,255,0.2);
        color: white;
    }
    
    #sidebarToggle:hover {
        background-color: #007bff;
    }
    
    /* Mobile menu spacing */
    .sidebar .navbar-brand {
        margin-bottom: 5px;
    }
    
    /* Show nav only when not collapsed */
    .sidebar:not(.sidebar-collapsed) .nav {
        display: block;
    }
    
    .sidebar.sidebar-collapsed .nav {
        display: none;
    }
    
    /* Adjust icon and text display for mobile */
    .sidebar a {
        padding: 8px 15px !important;
        font-size: 0.9rem;
    }
    
    .sidebar .menu-text {
        display: inline-block !important;
    }
    
    /* Hide section headings on collapsed mobile */
    .sidebar.sidebar-collapsed .sidebar-heading {
        display: none;
    }
    
    /* Enhanced touch targets for better mobile experience */
    button,
    .btn,
    .form-control,
    select,
    input[type="checkbox"],
    input[type="radio"],
    .nav-link,
    .dropdown-item,
    .page-link {
        min-height: 44px;
        min-width: 44px;
        padding: 10px;
        touch-action: manipulation;
    }
    
    /* Improved form controls for mobile */
    input, select, textarea {
        font-size: 16px !important; /* Prevents iOS zoom on focus */
    }
    
    /* Better table display on mobile */
    .table-responsive {
        overflow-x: auto;
        -webkit-overflow-scrolling: touch;
    }
    
    /* Card improvements for mobile */
    .card {
        margin-bottom: 1.5rem;
    }
    
    .card-body {
        padding: 1rem;
    }
    
    /* Improved sidebar for mobile */
    .sidebar {
        width: 100%;
        max-width: 100%;
        position: fixed;
        z-index: 1030;
        top: 0;
        left: -100%;
        height: 100%;
        transition: all 0.3s ease;
    }
    
    .sidebar.show {
        left: 0;
    }
    
    .main-content {
        margin-left: 0 !important;
        width: 100% !important;
        /* Explicit symmetric padding for small mobile layout */
        padding-top: var(--admin-vertical-spacing) !important;
        padding-bottom: var(--admin-vertical-spacing) !important;
        padding-left: var(--admin-content-padding-small) !important;
        padding-right: var(--admin-content-padding-small) !important;
    }
    
    /* Floating action button for mobile */
    .mobile-fab {
        display: block;
        position: fixed;
        bottom: 20px;
        right: 20px;
        width: 56px;
        height: 56px;
        border-radius: 50%;
        background-color: var(--primary);
        color: white;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 1000;
        font-size: 24px;
    }
    
    /* Improved calendar for mobile */
    #calendar {
        font-size: 0.8rem;
    }
    
    .fc-toolbar-title {
        font-size: 1rem !important;
    }
    
    .fc-button {
        padding: 0.3rem 0.5rem !important;
        font-size: 0.8rem !important;
    }
    
    /* Improved modal for mobile */
    .modal-dialog {
        margin: 0.5rem;
    }

    .modal-content {
        border-radius: 0.5rem;
    }
}
    
    /* Improved pagination for mobile */
    .pagination {
        flex-wrap: wrap;
        justify-content: center;
    }
    
    .page-link {
        margin: 2px;
    }
}

/* Small mobile devices */
@media (max-width: 576px) {
    .card-header {
        padding: 0.75rem;
    }
    
    .card-body {
        padding: 0.75rem;
    }
    
    h1, .h1 {
        font-size: 1.5rem;
    }
    
    h2, .h2 {
        font-size: 1.3rem;
    }
    
    h3, .h3 {
        font-size: 1.1rem;
    }
    
    /* Stack buttons on small screens */
    .btn-group {
        flex-direction: column;
        align-items: stretch;
    }
    
    .btn-group .btn {
        margin-bottom: 0.5rem;
        border-radius: 0.25rem !important;
    }
    
    /* Adjust form layout for small screens */
    .form-row {
        flex-direction: column;
    }
    
    .form-row > .col,
    .form-row > [class*="col-"] {
        padding-right: 0;
        padding-left: 0;
    }
    
    /* Adjust table display for very small screens */
    .table th,
    .table td {
        padding: 0.5rem;
        font-size: 0.8rem;
    }
    
    /* Adjust calendar for very small screens */
    .fc-daygrid-day-number {
        font-size: 0.7rem;
    }
    
    .fc-event {
        font-size: 0.7rem;
    }
}

/* Improved focus states for accessibility */
a:focus,
button:focus,
input:focus,
select:focus,
textarea:focus {
    outline: 2px solid #007bff;
    outline-offset: 2px;
}

/* Add smooth scrolling for all elements */
* {
    -webkit-overflow-scrolling: touch;
}

/* Reduced motion preference */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }
}

/* Add support for notch displays - Maintain Symmetric Padding */
@supports (padding: max(0px)) {
    .main-content {
        /* Maintain symmetric padding even with safe area insets */
        padding-left: max(var(--admin-content-padding-desktop), env(safe-area-inset-left));
        padding-right: max(var(--admin-content-padding-desktop), env(safe-area-inset-right));
    }

    /* Adjust for mobile screens with notch */
    @media (max-width: 768px) {
        .main-content {
            padding-left: max(var(--admin-content-padding-mobile), env(safe-area-inset-left));
            padding-right: max(var(--admin-content-padding-mobile), env(safe-area-inset-right));
        }
    }

    /* Adjust for very small screens with notch */
    @media (max-width: 480px) {
        .main-content {
            padding-left: max(var(--admin-content-padding-small), env(safe-area-inset-left));
            padding-right: max(var(--admin-content-padding-small), env(safe-area-inset-right));
        }
    }
    
    .mobile-fab {
        bottom: max(20px, env(safe-area-inset-bottom));
        right: max(20px, env(safe-area-inset-right));
    }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
    .dark-mode-enabled {
        --bg-color: #121212;
        --text-color: #f5f5f5;
        --card-bg: #1e1e1e;
        --border-color: #333;
    }
    
    .dark-mode-enabled body {
        background-color: var(--bg-color);
        color: var(--text-color);
    }
    
    .dark-mode-enabled .card {
        background-color: var(--card-bg);
        border-color: var(--border-color);
    }
    
    .dark-mode-enabled .table {
        color: var(--text-color);
    }
    
    .dark-mode-enabled .table-hover tbody tr:hover {
        background-color: rgba(255, 255, 255, 0.05);
    }
}

/* PWA display improvements */
@media (display-mode: standalone) {
    body {
        overscroll-behavior: none;
    }
    
    .pwa-indicator {
        display: block;
    }
}

/* PWA indicator */
.pwa-indicator {
    display: none;
    position: fixed;
    top: 10px;
    right: 10px;
    background-color: rgba(0, 123, 255, 0.1);
    color: #007bff;
    padding: 5px 10px;
    border-radius: 20px;
    font-size: 0.7rem;
    z-index: 1000;
}

/* Card Styles */
.card {
    margin-bottom: 20px;
    border-radius: 8px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    border: none;
}

.card-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid #eee;
    padding: 15px 20px;
    font-weight: 600;
}

/* Table Styles */
.table th {
    background-color: #f8f9fa;
    font-weight: 600;
}

/* Form Styles */
.form-label {
    font-weight: 500;
}

.btn-primary {
    background-color: #007bff;
    border-color: #007bff;
}

.btn-primary:hover {
    background-color: #0069d9;
    border-color: #0062cc;
}

/* Stats Card Styles */
.stat-card {
    transition: transform 0.3s;
    border-radius: 8px;
}

.stat-card:hover {
    transform: translateY(-5px);
}

.stat-value {
    font-size: 2.5rem;
    font-weight: 700;
}

.stat-label {
    font-size: 1rem;
    opacity: 0.8;
    margin-top: 5px;
}

/* Chart Styles */
.chart-container {
    height: 300px;
    width: 100%;
}

/* Ultra-Compact Calendar Styles */
#calendar {
    font-size: 0.8rem;
    height: auto !important;
    min-height: 330px !important; /* Reduced height to avoid overflow */
    max-height: 350px !important; /* Maximum height to prevent overflow */
    overflow: visible;
    width: 100%;
}

/* Make calendar fit properly in container */
.fc {
    max-width: 100%;
    overflow: visible !important;
    height: auto !important;
    min-height: 330px !important; /* Match calendar min-height */
    max-height: 350px !important; /* Match calendar max-height */
}

.fc .fc-view-harness {
    height: auto !important;
    min-height: unset !important; /* Remove min-height constraint */
    max-height: 310px !important; /* Smaller than calendar max-height */
    overflow: visible !important;
}

.fc-scrollgrid-liquid {
    height: auto !important;
}

/* Ensure table width is set to prevent layout issues */
.fc table {
    width: 100% !important;
    table-layout: fixed !important;
}

/* Enhance border aesthetics for the grid with inner margin */
.fc-theme-standard .fc-scrollgrid {
    border: none !important;
    margin: 5px !important;
}

.fc-theme-standard td, .fc-theme-standard th {
    border: 1px solid #dee2e6 !important;
}

/* Style the header to be more modern */
.fc-col-header-cell {
    background-color: #f8f9fa;
    padding: 3px 0 !important; /* Even smaller padding */
    font-weight: 600;
    font-size: 0.7rem !important; /* Smaller font */
}

/* Improve weekend styling */
.fc-day-sat, .fc-day-sun {
    background-color: #f8f9fa;
}

/* Improve hover effect on date cells */
.fc-daygrid-day:hover {
    background-color: rgba(0, 123, 255, 0.05);
}

/* Better styling for today */
.fc-day-today {
    background-color: rgba(0, 123, 255, 0.1) !important;
}

/* Ultra-compact cell sizing to fit all days */
.fc .fc-daygrid-day {
    max-height: none !important;
    min-height: 45px !important; /* Reduced height */
    height: 45px !important; /* Fixed height for consistency */
}

/* Force extra small day cells to fit all days */
.fc-daygrid-day-frame {
    min-height: 45px !important; /* Match day cell height */
    height: 45px !important; /* Fixed height for consistency */
    padding: 2px !important;
    overflow: hidden; /* Prevent content overflow */
}

/* Better day number styling - more compact */
.fc .fc-daygrid-day-number {
    padding: 1px 3px !important; /* Smaller padding */
    font-size: 0.7rem !important; /* Smaller font */
    font-weight: 500;
    color: #495057;
    margin: 1px;
}

/* Fix the 6th week display issue */
.fc-dayGridMonth-view .fc-daygrid-body {
    min-height: unset !important; /* Remove min-height constraint */
    height: auto !important;
    max-height: 290px !important; /* Control max height */
}

/* Keep all rows visible */
.fc-scrollgrid-sync-table {
    height: auto !important;
}

/* Ensure scrollgrid structure height doesn't collapse */
.fc-scrollgrid {
    height: auto !important;
    border-collapse: separate !important;
    border-spacing: 0;
}

/* Make scroller visible and auto height */
.fc-scroller {
    overflow: visible !important;
    height: auto !important;
}

/* Remove scrollbars from calendar */
.fc .fc-scroller-liquid-absolute {
    position: relative !important;
    overflow: visible !important;
    height: auto !important;
}

/* Improve the toolbar appearance - make it smaller */
.fc-toolbar {
    margin-bottom: 0.5rem !important;
}

.fc-toolbar-title {
    font-size: 1rem !important; /* Smaller title */
    font-weight: 600 !important;
    color: #212529;
}

/* Style the buttons in the toolbar - smaller buttons */
.fc-button-primary {
    background-color: #6c757d !important;
    border-color: #6c757d !important;
    box-shadow: none !important;
    padding: 0.25rem 0.5rem !important; /* Smaller padding */
    font-size: 0.75rem !important; /* Smaller font */
}

.fc-button-primary:hover {
    background-color: #5a6268 !important;
    border-color: #545b62 !important;
}

.fc-button-primary:not(:disabled):active,
.fc-button-primary:not(:disabled).fc-button-active {
    background-color: #495057 !important;
    border-color: #495057 !important;
}

/* Ultra-compact birthday event styling */
.fc-event.birthday-event {
    background-color: rgba(255, 193, 7, 0.1) !important;
    border-left: 3px solid #ffc107 !important;
    border-top: none !important;
    border-bottom: none !important;
    border-right: none !important;
    color: #212529 !important;
    font-weight: 500 !important;
    padding: 1px 2px !important;
    margin: 1px 0 !important;
    border-radius: 2px;
    cursor: pointer;
    height: 18px !important;
    max-height: 18px !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
    white-space: nowrap !important;
}

.birthday-title {
    padding-left: 14px !important;
    position: relative;
    font-size: 0.7rem !important;
    line-height: 1.1 !important;
    white-space: nowrap !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
    max-width: 95% !important;
}

.birthday-title:before {
    content: "🎂";
    position: absolute;
    left: 2px;
    top: 50%;
    transform: translateY(-50%);
    font-size: 0.7rem !important;
}

/* Enhanced Recent Members Styling */
.table-recent-members {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;
    margin: 0;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.table-recent-members thead {
    background-color: #f8f9fa;
}

.table-recent-members th {
    padding: 12px 8px;
    font-weight: 600;
    font-size: 0.85rem;
    color: #495057;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    border-bottom: 2px solid #dee2e6;
}

.table-recent-members td {
    padding: 10px 8px;
    font-size: 0.9rem;
    color: #212529;
    border-bottom: 1px solid #e9ecef;
    vertical-align: middle;
}

.table-recent-members tr:last-child td {
    border-bottom: none;
}

.table-recent-members tr:hover {
    background-color: #f8f9fa;
}

/* Member name styling */
.member-name {
    font-weight: 500;
    color: #212529;
}

/* Member joined date styling */
.member-joined {
    font-size: 0.8rem;
    color: #6c757d;
    background-color: #f8f9fa;
    padding: 2px 6px;
    border-radius: 4px;
    display: inline-block;
}

/* Email styling */
.member-email {
    color: #6c757d;
    font-size: 0.85rem;
}

/* Status indicators */
.member-status {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    display: inline-block;
    margin-right: 6px;
}

.status-active {
    background-color: #28a745;
}

.status-inactive {
    background-color: #dc3545;
}

/* Quick Links */
.btn-outline-primary, 
.btn-outline-warning, 
.btn-outline-info, 
.btn-outline-success {
    transition: all 0.3s;
}

.btn-outline-primary:hover, 
.btn-outline-warning:hover, 
.btn-outline-info:hover, 
.btn-outline-success:hover {
    transform: translateY(-3px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* Icons in sidebar */
.sidebar .bi {
    width: 18px;
    text-align: center;
    display: inline-block;
}

/* Additional sidebar styling */
.sidebar a {
    color: #f8f9fa;
    padding: 8px 15px;
    display: block;
    text-decoration: none;
    transition: background-color 0.3s;
    margin-bottom: 4px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.sidebar a:hover {
    background-color: #495057;
    border-radius: 5px;
}

.sidebar a.active {
    background-color: #007bff;
    border-radius: 5px;
}

.navbar-brand {
    font-weight: bold;
    color: white;
    padding-left: 15px;
    margin-bottom: 15px;
    font-size: 1.4rem;
}

.sidebar-heading {
    font-size: 0.7rem;
    letter-spacing: 0.1rem;
    padding: 0.4rem 0.8rem;
    color: rgba(255, 255, 255, 0.5) !important;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.sidebar-content {
    width: 100%;
    padding-left: 5px;
    padding-right: 5px;
    /* Removed overflow: visible to allow scrolling */
}

/* Cross-browser compatibility fixes */
html, body {
    overflow-x: hidden;
}

/* Ensure the main wrapper doesn't cause overflow issues */
#wrapper {
    width: 100%;
    overflow-x: hidden;
}

/* Additional safety for content containers */
.main-content > * {
    max-width: 100%;
    box-sizing: border-box;
}

/* Additional main-content properties for consistency */
.main-content {
    box-sizing: border-box;
    overflow-x: hidden;
}

/* Ensure there's no overflow hiding in sidebar items */
.sidebar .nav-item {
    overflow: visible;
    position: relative;
}

/* Card equal height */
.card.h-100 {
    height: 100%;
    display: flex;
    flex-direction: column;
}

.card.h-100 .card-body {
    flex: 1 1 auto;
    overflow: auto;
}

/* Additional Calendar fixes */
.fc-event.fc-event-main-frame {
    display: block;
    z-index: 3;
    position: relative;
}

.fc .fc-daygrid-event-harness {
    margin-top: 1px;
    margin-bottom: 1px;
}

.fc-h-event .fc-event-title {
    display: block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    padding: 0 1px;
}

.birthday-event .fc-event-title {
    font-weight: bold !important;
}

/* Make sure birthday indicator shows */
.birthday-event:before {
    content: "🎂" !important;
    display: inline-block !important;
    margin-right: 3px !important;
    position: absolute !important;
    left: 2px !important;
}

.fc-daygrid-day-events {
    min-height: 2em;
}

/* Give more space to calendar cells */
.fc td, .fc th {
    padding: 1px;
    vertical-align: top;
    min-height: 30px;
}

.fc .fc-daygrid-day-top {
    display: flex;
    flex-direction: row-reverse;
    padding: 3px;
}

/* Modern Calendar Styling */
.card.h-100 {
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    border: none;
    border-radius: 8px;
    overflow: hidden;
}

.card-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
    padding-top: 12px;
    padding-bottom: 12px;
}

/* Calendar toolbar styling */
.fc .fc-toolbar.fc-header-toolbar {
    margin-bottom: 10px;
    padding-bottom: 8px;
}

.fc .fc-toolbar-title {
    font-size: 1.15rem !important;
    font-weight: 600;
    color: #333;
}

.fc .fc-button-primary {
    background-color: #3788d8 !important;
    border-color: #3788d8 !important;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    border-radius: 4px;
    padding: 4px 8px;
    font-size: 0.85rem;
    font-weight: 500;
    transition: all 0.2s ease;
}

.fc .fc-button-primary:hover {
    background-color: #2c6cb5 !important;
    border-color: #2c6cb5 !important;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.15);
}

.fc .fc-button-primary:not(:disabled):active {
    background-color: #1a4b8e !important;
    border-color: #1a4b8e !important;
}

/* Calendar Cell Styling */
.fc-daygrid-day.fc-day-today {
    background-color: rgba(55, 136, 216, 0.1) !important;
}

.fc-daygrid-day.fc-day-other {
    background-color: #f8f9fa;  /* Light gray for days from other months */
}

.fc-col-header-cell {
    background-color: #f2f6fc;
    padding: 8px 0 !important;
    font-weight: 600;
    color: #495057;
}

/* Make today's date stand out */
.fc-day-today .fc-daygrid-day-number {
    background-color: #3788d8;
    color: white;
    font-weight: 600;
    width: 24px;
    height: 24px;
    line-height: 24px;
    text-align: center;
    border-radius: 50%;
    display: inline-block;
}

/* Birthday Events */
.fc-event.birthday-event {
    background-color: #ffc107 !important;
    border-color: #e0a800 !important;
    color: #212529 !important;
    font-weight: 600 !important;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    border-radius: 4px;
    border-left: 3px solid #ff9800;
    transition: transform 0.2s ease;
}

.fc-event.birthday-event:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 5px rgba(0,0,0,0.15);
    background-color: #ffca2c !important;
}

/* Test Events */
.fc-event.test-event {
    background-color: #dc3545 !important;
    border-color: #c82333 !important;
    color: white !important;
    font-weight: 600 !important;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    border-radius: 4px;
    border-left: 3px solid #bd2130;
    transition: transform 0.2s ease;
}

.fc-event.test-event:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 5px rgba(0,0,0,0.15);
}

/* Ensure the birthday cake emoji displays correctly */
.birthday-event:before {
    content: "🎂" !important;
    display: inline-block !important;
    margin-right: 3px !important;
    position: absolute !important;
    left: 2px !important;
    font-size: 10px !important;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .fc .fc-toolbar {
        flex-direction: column;
        gap: 8px;
    }
    
    .fc .fc-toolbar-title {
        font-size: 1rem !important;
    }
    
    .fc .fc-button {
        padding: 3px 6px;
        font-size: 0.8rem;
    }
    
    .fc-daygrid-day-number {
        font-size: 0.75rem;
        padding: 1px !important;
    }
}

/* Additional birthday styles */
.birthday-title {
    padding-left: 15px !important;
    position: relative;
}

.birthday-title:before {
    content: "🎂";
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
}

/* Ensure table width */
.table {
    width: 100% !important;
    margin-bottom: 0;
}

/* Make sure calendar grid fits properly */
.fc .fc-scrollgrid-section-body table, 
.fc .fc-scrollgrid-section-header table {
    width: 100% !important;
}

/* Ensure proper cell sizing */
.fc-day-sat, .fc-day-sun {
    background-color: #fafafa;
}

/* Make sure grid lines look better */
.fc-theme-standard td, .fc-theme-standard th {
    border: 1px solid #e1e5eb !important;
}

/* Adjust spacing in the grid for better appearance */
.fc .fc-daygrid-day-frame {
    padding: 2px !important;
    min-height: 35px;
}

/* Fixed table layout for more consistent rendering */
.fc-scrollgrid {
    table-layout: fixed !important;
}

/* Better focus handling on buttons */
.fc .fc-button:focus {
    box-shadow: 0 0 0 0.2rem rgba(55, 136, 216, 0.25) !important;
    outline: none !important;
}

/* Birthday badge in celebrants table */
.table .me-2 {
    color: #ffc107;
}

/* Hover effects for table rows */
.table-hover tbody tr:hover {
    background-color: rgba(55, 136, 216, 0.05);
}

/* Make the card shadow subtle */
.card.h-100 {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    transition: box-shadow 0.3s ease;
}

.card.h-100:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.12);
}

/* Enhanced Calendar Container */
.calendar-container {
    padding: 0 !important; /* Remove padding that causes overflow */
    margin: 0 !important; /* Remove margin that causes overflow */
    border-radius: 8px;
    overflow: hidden; /* Contain all content */
}

/* Clear Grid Lines with Subtle Color */
.fc-theme-standard td, 
.fc-theme-standard th {
    border: 1px solid #e2e8f0 !important;
}

/* Enhanced Day Number Styling */
.fc .fc-daygrid-day-number {
    color: #1e293b;
    font-weight: 600;
    font-size: 0.75rem !important;
    padding: 2px 4px !important;
    background-color: rgba(255, 255, 255, 0.8);
    border-radius: 4px;
    margin: 1px;
}

/* Header Styling */
.fc-col-header-cell {
    background-color: #f1f5f9 !important;
    padding: 4px 0 !important;
}

.fc-col-header-cell .fc-scrollgrid-sync-inner {
    font-weight: 600;
    color: #475569;
    text-transform: uppercase;
    font-size: 0.7rem !important;
}

/* Hover Effects */
.fc-daygrid-day:hover .fc-daygrid-day-number {
    background-color: #eff6ff;
}

/* Today's Date Highlight */
.fc-day-today {
    background-color: #f0f9ff !important;
}

.fc-day-today .fc-daygrid-day-number {
    color: #0369a1;
    background-color: #e0f2fe;
}

/* Birthday Event Styling */
.birthday-event {
    border-left: 3px solid #f59e0b !important;
    background-color: #fffbeb !important;
}

/* Dashboard Stats Enhancement */
.card-dashboard {
    border: 1px solid #e9ecef;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.card-dashboard:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
}

/* Recent Members Table */
.table-hover tbody tr {
    transition: background-color 0.2s ease;
}

.table-hover tbody tr:hover {
    background-color: #f8fafc;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    .fc-toolbar-title {
        font-size: 1.1rem !important;
    }
    
    .fc .fc-button {
        padding: 0.25rem 0.5rem;
        font-size: 0.8rem;
    }
    
    .card-dashboard .bi {
        font-size: 1.5rem !important;
    }
}

/* Enhanced Dashboard Styles */
.card-dashboard {
    border: 1px solid #e9ecef;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.card-dashboard:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
}

.stat-card {
    background: white;
    border: 1px solid rgba(0, 0, 0, 0.05);
    transition: transform 0.3s ease;
}

.icon-container {
    width: 50px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.bg-light-blue { background-color: #e3f2fd; }
.bg-light-warning { background-color: #fff3cd; }
.bg-light-success { background-color: #d4edda; }
.bg-light-purple { background-color: #e9d8fd; }
.bg-purple { background-color: #6f42c1; }

.stat-value {
    font-size: 1.8rem;
    font-weight: 700;
    color: #2c3e50;
}

.stat-label {
    font-size: 0.9rem;
    color: #6c757d;
    letter-spacing: 0.5px;
}

/* Data Grid Improvements */
.data-grid {
    display: grid;
    gap: 1.5rem;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
}

/* Recent Members Card */
.recent-members-card .table {
    margin-bottom: 0;
}

.recent-members-card .table td {
    vertical-align: middle;
    padding: 0.75rem;
}

.member-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    object-fit: cover;
    border: 2px solid #f8f9fa;
    margin-right: 12px;
}

/* Birthday Celebrants Section */
.birthday-card .badge {
    background-color: #fff3cd;
    color: #856404;
    font-weight: 500;
}

.birthday-item {
    display: flex;
    align-items: center;
    padding: 0.75rem 1rem;
    border-radius: 8px;
    transition: background-color 0.2s ease;
}

.birthday-item:hover {
    background-color: #f8f9fa;
}

.birthday-date {
    font-size: 0.85rem;
    color: #6c757d;
    min-width: 70px;
}

/* Enhanced Recent Members Section */
.recent-members-card {
    border: 1px solid #e9ecef;
    border-radius: 12px;
    overflow: hidden;
}

.recent-members-card .table {
    margin-bottom: 0;
    border-collapse: separate;
    border-spacing: 0;
}

.recent-members-card th {
    background-color: #f8f9fa;
    font-weight: 600;
    color: #495057;
    padding: 12px 15px;
    border-bottom: 2px solid #dee2e6;
}

.recent-members-card td {
    padding: 12px 15px;
    vertical-align: middle;
    border-bottom: 1px solid #e9ecef;
    background-color: white;
}

.recent-members-card tr:last-child td {
    border-bottom: 0;
}

.recent-members-card tr:hover td {
    background-color: #f8fafc;
}

/* Email Test Configuration Styling */
.email-test-card {
    border: none;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
    overflow: hidden;
}

.email-test-card:hover {
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.12);
}

.email-test-card .card-header {
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    padding: 1rem 1.25rem;
}

.email-test-card .badge {
    font-size: 0.75rem;
    font-weight: 600;
    padding: 0.35em 0.65em;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.email-test-form .form-label {
    font-weight: 500;
    font-size: 0.875rem;
    color: #495057;
    margin-bottom: 0.5rem;
}

.email-test-form .input-group {
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
    border-radius: 6px;
    transition: all 0.2s ease;
}

.email-test-form .input-group:focus-within {
    box-shadow: 0 2px 8px rgba(13, 110, 253, 0.15);
}

.email-test-form .input-group-text {
    background-color: #f8f9fa;
    color: #6c757d;
    border: 1px solid #ced4da;
    border-right: none;
}

.email-test-form .form-control {
    border-left: none;
    padding: 0.6rem 0.75rem;
    height: auto;
}

.email-test-form .form-control:focus {
    box-shadow: none;
    border-color: #ced4da;
}

.email-test-form .btn-primary {
    padding: 0.6rem 1rem;
    font-weight: 500;
    box-shadow: 0 2px 6px rgba(13, 110, 253, 0.2);
    transition: all 0.2s ease;
}

.email-test-form .btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(13, 110, 253, 0.3);
}

/* Accordion Styling for Email Tips */
.email-test-card .accordion-item {
    border: 1px solid rgba(0, 0, 0, 0.05);
    border-radius: 6px;
    overflow: hidden;
    margin-bottom: 0.75rem;
}

.email-test-card .accordion-button {
    font-weight: 500;
    color: #495057;
    background-color: #f8f9fa;
    padding: 0.75rem 1.25rem;
}

.email-test-card .accordion-button:not(.collapsed) {
    color: #0d6efd;
    background-color: rgba(13, 110, 253, 0.05);
}

.email-test-card .accordion-button:focus {
    box-shadow: none;
    border-color: rgba(13, 110, 253, 0.1);
}

.email-test-card .accordion-body {
    padding: 1rem;
    background-color: #fff;
}

.email-test-card .list-group-flush .list-group-item {
    padding: 0.75rem 0;
    border-color: rgba(0, 0, 0, 0.05);
}

/* Responsive adjustments for email test form */
@media (max-width: 992px) {
    .email-test-form .row {
        flex-direction: column;
    }
    
    .email-test-form .col-md-3,
    .email-test-form .col-md-4,
    .email-test-form .col-md-5 {
        width: 100%;
        margin-bottom: 1rem;
    }
    
    .email-test-form .btn-primary {
        margin-top: 0.5rem;
    }
}

/* Modern Table Appearance */
.table-modern {
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    overflow: hidden;
}

.table-modern th {
    background-color: #f8fafc;
    padding: 12px 16px;
    color: #64748b;
    font-weight: 600;
    text-transform: uppercase;
    font-size: 0.75rem;
    letter-spacing: 0.5px;
}

.table-modern td {
    padding: 12px 16px;
    color: #475569;
    font-size: 0.9rem;
}

/* Email Test Form Layout */
.form-grid {
    display: grid;
    grid-template-columns: 1fr auto;
    gap: 12px;
    align-items: start;
}

@media (max-width: 576px) {
    .form-grid {
        grid-template-columns: 1fr;
    }
}

/* Calendar Container Adjustments */
.calendar-container {
    padding: 0 !important; /* Remove padding that causes overflow */
    margin: 0 !important; /* Remove margin that causes overflow */
    border-radius: 8px;
    overflow: hidden; /* Contain all content */
}

/* Compact Calendar Cells */
.fc .fc-daygrid-day {
    min-height: 30px !important;  /* Reduced from 40px */
    padding: 1px !important;
}

.fc .fc-daygrid-day-frame {
    min-height: 30px !important;  /* Reduced from 35px */
    padding: 1px !important;
}

.fc .fc-daygrid-day-number {
    font-size: 0.75rem !important;  /* Smaller font */
    padding: 2px 4px !important;    /* Reduced padding */
    margin: 1px;
}

/* Compact Header */
.fc .fc-col-header-cell {
    padding: 4px 0 !important;
}

.fc .fc-col-header-cell-cushion {
    font-size: 0.7rem !important;
    padding: 2px 4px !important;
}

/* Smaller Toolbar */
.fc .fc-toolbar-title {
    font-size: 1rem !important;
}

.fc .fc-button {
    padding: 0.25rem 0.5rem !important;
    font-size: 0.7rem !important;
}

/* Compact Events */
.fc-event {
    margin: 0.5px 0 !important;
    font-size: 0.65rem !important;
    line-height: 1.2;
}

.birthday-event:before {
    font-size: 0.6rem !important;
}

/* Additional Calendar Container Styling */
.calendar-wrapper {
    padding: 8px;
    border-radius: 8px;
    background-color: #fff;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
    margin-bottom: 1.5rem;
}

.calendar-wrapper .card-header {
    background-color: transparent;
    border-bottom: 1px solid #e9ecef;
    padding: 0.75rem 1rem;
}

.calendar-inner {
    padding: 5px;
}

/* Member Avatar Placeholder Styling */
.member-avatar-placeholder {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: #e9ecef;
    color: #6c757d;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 500;
    font-size: 1.2rem;
}

/* Colorize avatar placeholders based on first letter */
.member-avatar-placeholder:nth-child(5n+1) {
    background-color: #007bff;
}

.member-avatar-placeholder:nth-child(5n+2) {
    background-color: #28a745;
}

.member-avatar-placeholder:nth-child(5n+3) {
    background-color: #fd7e14;
}

.member-avatar-placeholder:nth-child(5n+4) {
    background-color: #6f42c1;
}

.member-avatar-placeholder:nth-child(5n+5) {
    background-color: #e83e8c;
}

/* Ultra-compact calendar view */
.full-calendar-compact .fc-daygrid-day-events {
    min-height: 0.5em !important;
    margin-top: 1px !important;
    margin-bottom: 1px !important;
}

.full-calendar-compact .fc-daygrid-body {
    width: 100% !important;
}

.full-calendar-compact .fc-scrollgrid {
    border-radius: 6px;
    overflow: hidden;
    border: 1px solid #dee2e6 !important;
}

.full-calendar-compact .fc-scrollgrid-section > td {
    border: none !important;
}

/* Make overall calendar more compact */
.full-calendar-compact {
    --fc-page-bg-color: #fff;
    --fc-border-color: #dee2e6;
    --fc-event-border-color: transparent;
    --fc-today-bg-color: rgba(0, 123, 255, 0.1);
    --fc-highlight-color: rgba(188, 232, 241, 0.3);
    height: auto !important;
}

.full-calendar-compact .fc-toolbar.fc-header-toolbar {
    margin-bottom: 0.5em !important;
}

/* Balanced Layout for Calendar and Birthday Sections */
.calendar-card, 
.birthday-card {
    height: 100%;
    display: flex;
    flex-direction: column;
    margin-bottom: 1.5rem;
}

.calendar-card .card-body,
.birthday-card .card-body {
    flex: 1;
    overflow: hidden;
    padding: 0.75rem;
}

/* Calendar size constraints - more compact display */
#calendar {
    font-size: 0.8rem;
    height: auto !important;
    min-height: 330px !important; /* Reduced height to avoid overflow */
    max-height: 350px !important; /* Maximum height to prevent overflow */
    overflow: visible;
    width: 100%;
}

/* Calendar container with better vertical space */
.fc {
    max-width: 100%;
    overflow: visible !important;
    height: auto !important;
    min-height: 330px !important; /* Match calendar min-height */
    max-height: 350px !important; /* Match calendar max-height */
}

.fc .fc-view-harness {
    height: auto !important;
    min-height: unset !important; /* Remove min-height constraint */
    max-height: 310px !important; /* Smaller than calendar max-height */
    overflow: visible !important;
}

/* Fix the display issue for all days */
.fc-dayGridMonth-view .fc-daygrid-body {
    min-height: unset !important; /* Remove min-height constraint */
    height: auto !important;
    max-height: 290px !important; /* Control max height */
}

/* More compact day cells to fit all days */
.fc .fc-daygrid-day {
    max-height: none !important;
    min-height: 45px !important; /* Reduced height */
    height: 45px !important; /* Fixed height for consistency */
}

.fc-daygrid-day-frame {
    min-height: 45px !important; /* Match day cell height */
    height: 45px !important; /* Fixed height for consistency */
    padding: 2px !important;
    overflow: hidden; /* Prevent content overflow */
}

/* Smaller day numbers but well positioned */
.fc .fc-daygrid-day-number {
    padding: 2px 4px !important;
    font-size: 0.8rem;
    margin: 1px;
    font-weight: 500;
}

/* Birthday section enhancements - match height with calendar */
.birthday-celebrants-list {
    max-height: 370px; /* Match the calendar's height */
    overflow-y: auto;
}

/* Ensure both sections have equal widths */
.calendar-section-col,
.birthday-section-col {
    width: 50%; /* Equal width for both sections */
}

/* Visualization Graphs Section */
.data-visualization {
    margin-bottom: 1.5rem;
}

.chart-container {
    position: relative;
    height: 280px;
    margin-bottom: 1rem;
}

.chart-card {
    height: 100%;
    border-radius: 0.5rem;
    box-shadow: 0 0.15rem 1.75rem rgba(0, 0, 0, 0.05);
    background-color: #fff;
    overflow: hidden;
    transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
}

.chart-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 0.5rem 1.5rem rgba(0, 0, 0, 0.1);
}

.chart-card .card-header {
    background-color: transparent;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    padding: 1rem 1.25rem;
}

.chart-card .card-header h6 {
    color: #4e73df;
    font-weight: 700;
    font-size: 1rem;
    margin: 0;
}

/* Chart Legend and Indicators */
.chart-legend-indicator {
    display: flex;
    align-items: center;
}

.chart-legend-indicator .badge {
    font-size: 0.7rem;
    font-weight: 600;
    padding: 0.35rem 0.65rem;
}

/* Data Visualization Row Spacing */
.data-visualization {
    padding-top: 0.5rem;
    padding-bottom: 1rem;
}

.data-visualization .chart-card {
    margin-bottom: 1rem;
}

/* Custom Chart Tooltips */
.chart-tooltip {
    background-color: rgba(255, 255, 255, 0.95) !important;
    color: #5a5c69 !important;
    border-radius: 0.5rem !important;
    padding: 0.75rem !important;
    box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.1) !important;
    border: 1px solid rgba(0, 0, 0, 0.05) !important;
    font-size: 0.875rem !important;
}

/* Responsive Chart Adjustments */
@media (max-width: 992px) {
    .chart-container {
        height: 240px;
    }
}

@media (max-width: 768px) {
    .chart-container {
        height: 220px;
    }
    
    .chart-card .card-header {
        padding: 0.75rem 1rem;
    }
    
    .chart-card .card-header h6 {
        font-size: 0.9rem;
    }
}

/* Enhanced Birthday Event Styling */
.fc-event.birthday-event {
    background-color: rgba(255, 193, 7, 0.1) !important;
    border-left: 3px solid #ffc107 !important;
    border-top: none !important;
    border-bottom: none !important;
    border-right: none !important;
    color: #212529 !important;
    font-weight: 500 !important;
    padding: 1px 2px !important;
    margin: 1px 0 !important;
    border-radius: 2px;
    cursor: pointer;
    height: 18px !important;
    max-height: 18px !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
    white-space: nowrap !important;
}

.birthday-title {
    padding-left: 14px !important;
    position: relative;
    font-size: 0.7rem !important;
    line-height: 1.1 !important;
    white-space: nowrap !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
    max-width: 95% !important;
}

.birthday-title:before {
    content: "🎂";
    position: absolute;
    left: 2px;
    top: 50%;
    transform: translateY(-50%);
    font-size: 0.7rem !important;
}

/* Better Birthday Section Styling */
.birthday-celebrants-list {
    max-height: 370px;
    overflow-y: auto;
}

.birthday-celebrants-list .list-group-item {
    padding: 0.75rem;
    border-left: none;
    border-right: none;
    border-radius: 0;
}

.birthday-celebrants-list .list-group-item:first-child {
    border-top: none;
}

.birthday-celebrants-list .list-group-item:hover {
    background-color: rgba(0, 0, 0, 0.02);
}

/* Day Cell Styling */
.fc-day-today {
    background-color: rgba(0, 123, 255, 0.1) !important;
}

.fc-day-sat, .fc-day-sun {
    background-color: #f8f9fa;
}

/* Header Row Styling */
.fc-col-header-cell {
    background-color: #f8f9fa;
    border-bottom: 2px solid #dee2e6 !important;
    padding: 8px 0 !important;
}

.fc-col-header-cell-cushion {
    font-weight: 600;
    color: #495057;
}

/* Button Styling */
.fc-button-primary {
    background-color: #6c757d !important;
    border-color: #6c757d !important;
    padding: 0.375rem 0.75rem !important;
    font-size: 0.875rem !important;
}

.fc-button-primary:hover {
    background-color: #5a6268 !important;
    border-color: #545b62 !important;
}

.fc-toolbar-title {
    font-size: 1.25rem !important;
    font-weight: 600 !important;
}

/* Collapsible Sidebar Categories */
.category-header a {
    cursor: pointer;
    text-decoration: none;
    color: rgba(255, 255, 255, 0.6);
    transition: color 0.3s;
}

.category-header a:hover {
    color: rgba(255, 255, 255, 0.9);
}

.category-header .toggle-icon {
    transition: transform 0.3s;
    font-size: 0.75rem;
}

.category-header a[aria-expanded="true"] .toggle-icon {
    transform: rotate(180deg);
}

/* Indent submenu items */
.sidebar .collapse {
    padding-left: 15px;
}

.sidebar.collapsed .collapse {
    padding-left: 0;
}

/* Category headers are completely hidden when collapsed, so these styles are not needed */

/* Tooltip for collapsed sidebar items */
.sidebar.collapsed a {
    position: relative;
}

.sidebar.collapsed a:hover::after {
    content: attr(title);
    position: absolute;
    left: 100%;
    top: 0;
    background: #343a40;
    color: white;
    padding: 5px 10px;
    border-radius: 3px;
    white-space: nowrap;
    z-index: 1000;
    box-shadow: 0 2px 5px rgba(0,0,0,0.2);
    margin-left: 10px;
}

/* ========================================
   MODAL Z-INDEX AND POSITIONING FIXES
   ======================================== */

/*
 * Comprehensive Modal Z-Index Hierarchy:
 * - Main content: z-index: 1
 * - Sidebar (desktop): z-index: 100
 * - Tooltips: z-index: 1000
 * - Mobile FAB: z-index: 1000
 * - Admin top nav (mobile): z-index: 1020
 * - Sidebar (mobile): z-index: 1030
 * - Modal backdrop: z-index: 1040
 * - Modals: z-index: 1050
 * - Multiple modals: z-index: 1055, 1060, etc.
 */

/* Base modal styling with proper z-index */
.modal {
    z-index: 1050 !important;
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    width: 100% !important;
    height: 100% !important;
    overflow-x: hidden !important;
    overflow-y: auto !important;
    outline: 0 !important;
}

/* Modal backdrop with proper z-index - RESTORED */
.modal-backdrop {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    z-index: 1040 !important;
    width: 100vw !important;
    height: 100vh !important;
    background-color: #000 !important;
    opacity: 0.5 !important;
    display: block !important; /* Restore backdrop visibility */
}

/* Ensure modal backdrop fades properly */
.modal-backdrop.fade {
    opacity: 0;
}

.modal-backdrop.show {
    opacity: 0.5;
}

/* Modal stacking for multiple modals */
.modal:nth-of-type(even) {
    z-index: 1055 !important;
}

.modal:nth-of-type(even) + .modal-backdrop {
    z-index: 1045 !important;
}

/* Third modal and beyond */
.modal.modal-stack-3 {
    z-index: 1060 !important;
}

.modal.modal-stack-3 + .modal-backdrop {
    z-index: 1050 !important;
}

/* Modal dialog positioning */
.modal-dialog {
    position: relative !important;
    width: auto !important;
    margin: 1.75rem !important;
    pointer-events: none !important;
}

/* Modal content styling */
.modal-content {
    position: relative !important;
    display: flex !important;
    flex-direction: column !important;
    width: 100% !important;
    pointer-events: auto !important;
    background-color: var(--bs-modal-bg, #fff) !important;
    background-clip: padding-box !important;
    border: var(--bs-modal-border-width, 1px) solid var(--bs-modal-border-color, #dee2e6) !important;
    border-radius: var(--bs-modal-border-radius, 0.5rem) !important;
    outline: 0 !important;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075) !important;
}

/* Ensure modals are always above sidebar */
.modal {
    z-index: 1050 !important;
}

/* Desktop sidebar has z-index: 100, mobile has z-index: 1030 */
@media (max-width: 768px) {
    .modal {
        z-index: 1050 !important; /* Still above mobile sidebar (1030) */
    }

    .modal-backdrop {
        z-index: 1040 !important;
    }
}

/* Prevent body scroll when modal is open */
body.modal-open {
    overflow: hidden !important;
    padding-right: 0 !important;
}

/* Fix for modals appearing behind fixed elements */
.modal-dialog {
    z-index: inherit !important;
}

/* Ensure modal headers and footers don't interfere */
.modal-header,
.modal-body,
.modal-footer {
    position: relative !important;
    z-index: auto !important;
}

/* ========================================
   RESPONSIVE MODAL FIXES
   ======================================== */

/* Mobile modal adjustments */
@media (max-width: 576px) {
    .modal-dialog {
        margin: 0.5rem !important;
        max-width: calc(100% - 1rem) !important;
    }

    .modal-content {
        border-radius: 0.375rem !important;
    }

    /* Ensure modals don't get cut off on small screens */
    .modal {
        padding: 0 !important;
    }
}

/* Tablet modal adjustments */
@media (min-width: 577px) and (max-width: 768px) {
    .modal-dialog {
        margin: 1rem !important;
        max-width: calc(100% - 2rem) !important;
    }
}

/* ========================================
   MODAL ANIMATION IMPROVEMENTS
   ======================================== */

/* Smooth modal transitions */
.modal.fade .modal-dialog {
    transition: transform 0.3s ease-out !important;
    transform: translate(0, -50px) !important;
}

.modal.show .modal-dialog {
    transform: none !important;
}

/* Backdrop fade animation */
.modal-backdrop.fade {
    transition: opacity 0.15s linear !important;
}

/* ========================================
   MODAL CONTENT FIXES
   ======================================== */

/* Ensure modal content is scrollable when needed */
.modal-body {
    max-height: calc(100vh - 200px) !important;
    overflow-y: auto !important;
}

/* Fix for long modal titles */
.modal-title {
    word-wrap: break-word !important;
    overflow-wrap: break-word !important;
}

/* Ensure close button is always accessible */
.modal-header .btn-close {
    position: relative !important;
    z-index: 1 !important;
    margin: -0.5rem -0.5rem -0.5rem auto !important;
}

/* ========================================
   ADMIN-SPECIFIC MODAL FIXES
   ======================================== */

/* Ensure modals work with admin top navigation */
.admin-top-nav + .modal {
    z-index: 1050 !important;
}

/* Fix for modals in admin tables */
.table .modal {
    z-index: 1050 !important;
}

/* Ensure dropdown menus in modals work properly */
.modal .dropdown-menu {
    z-index: 1055 !important;
}

/* Fix for tooltips in modals */
.modal .tooltip {
    z-index: 1060 !important;
}

/* ========================================
   ACCESSIBILITY IMPROVEMENTS
   ======================================== */

/* Focus management for modals */
.modal:focus {
    outline: none !important;
}

/* Ensure modal is announced to screen readers */
.modal[aria-modal="true"] {
    outline: none !important;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .modal-content {
        border: 2px solid !important;
    }

    .modal-backdrop {
        opacity: 0.8 !important;
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    .modal.fade .modal-dialog {
        transition: none !important;
    }

    .modal-backdrop.fade {
        transition: none !important;
    }
}

/* Enhanced Search and Filter Styles */
.search-filter-card {
    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
    border: 1px solid #e3e6f0;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
}

.search-filter-card:hover {
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
    transform: translateY(-1px);
}

.input-group-text {
    background: #f8f9fa !important;
    border-color: #e3e6f0;
    color: #6c757d;
}

.form-control:focus,
.form-select:focus {
    border-color: #4e73df;
    box-shadow: 0 0 0 0.2rem rgba(78, 115, 223, 0.25);
}

/* Active Filter Badges */
.badge.bg-primary-subtle {
    background-color: rgba(78, 115, 223, 0.1) !important;
    color: #4e73df !important;
    border: 1px solid rgba(78, 115, 223, 0.2) !important;
    padding: 0.5rem 0.75rem;
    font-size: 0.875rem;
    border-radius: 6px;
}

.badge.bg-info-subtle {
    background-color: rgba(54, 185, 204, 0.1) !important;
    color: #36b9cc !important;
    border: 1px solid rgba(54, 185, 204, 0.2) !important;
    padding: 0.5rem 0.75rem;
    font-size: 0.875rem;
    border-radius: 6px;
}

/* Gender Badge Styles in Member Details */
.badge.bg-primary {
    background: linear-gradient(135deg, #4e73df 0%, #224abe 100%) !important;
    border: none;
    padding: 0.5rem 0.75rem;
    font-size: 0.875rem;
    border-radius: 6px;
    box-shadow: 0 2px 4px rgba(78, 115, 223, 0.3);
}

.badge.bg-info {
    background: linear-gradient(135deg, #36b9cc 0%, #258391 100%) !important;
    border: none;
    padding: 0.5rem 0.75rem;
    font-size: 0.875rem;
    border-radius: 6px;
    box-shadow: 0 2px 4px rgba(54, 185, 204, 0.3);
}

/* Button Enhancements */
.btn-success {
    background: linear-gradient(135deg, #1cc88a 0%, #13855c 100%);
    border: none;
    box-shadow: 0 2px 4px rgba(28, 200, 138, 0.3);
    transition: all 0.3s ease;
}

.btn-success:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(28, 200, 138, 0.4);
}

.btn-primary {
    background: linear-gradient(135deg, #4e73df 0%, #224abe 100%);
    border: none;
    box-shadow: 0 2px 4px rgba(78, 115, 223, 0.3);
    transition: all 0.3s ease;
}

.btn-primary:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(78, 115, 223, 0.4);
}

/* Member Info List Enhancements */
.member-info li {
    padding: 0.75rem 0;
    border-bottom: 1px solid #f1f3f4;
    transition: all 0.2s ease;
}

.member-info li:hover {
    background-color: rgba(78, 115, 223, 0.05);
    padding-left: 0.5rem;
    border-radius: 6px;
    border-bottom: 1px solid transparent;
}

.member-info li:last-child {
    border-bottom: none;
}

/* Responsive Enhancements */
@media (max-width: 768px) {
    .search-filter-card .row {
        gap: 1rem;
    }

    .search-filter-card .col-md-3,
    .search-filter-card .col-md-6 {
        margin-bottom: 0.5rem;
    }

    .badge {
        font-size: 0.75rem;
        padding: 0.375rem 0.5rem;
    }
}

/* Animation for filter badges */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.badge {
    animation: fadeInUp 0.3s ease;
}

/* Button spacing improvements */
.btn-group + .btn,
.btn + .btn-group {
    margin-left: 0.75rem;
}

/* Ensure proper spacing in flex containers */
.d-flex.gap-3 > * {
    flex-shrink: 0;
}

/* Bulk actions container spacing */
#bulkActionsContainer {
    transition: all 0.3s ease;
}

/* Success message styling */
.alert-success {
    border-left: 4px solid #1cc88a;
    background: linear-gradient(135deg, rgba(28, 200, 138, 0.1) 0%, rgba(28, 200, 138, 0.05) 100%);
}

.alert-danger {
    border-left: 4px solid #e74a3b;
    background: linear-gradient(135deg, rgba(231, 74, 59, 0.1) 0%, rgba(231, 74, 59, 0.05) 100%);
}

/* ===== STICKY HEADER STYLES ===== */

/* Sticky Header Support */
.admin-top-nav {
    position: sticky !important;
    top: 0 !important;
    z-index: 1020 !important;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    transition: all 0.3s ease;
}

.admin-top-nav.scrolled {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
    background: linear-gradient(135deg, rgba(52, 58, 64, 0.95) 0%, rgba(73, 80, 87, 0.95) 100%) !important;
}

/* Smooth scrolling for main content */
.main-content {
    scroll-behavior: smooth;
}

/* Enhanced dropdown animation for sticky header */
.admin-top-nav .dropdown-menu {
    animation: fadeInDown 0.3s ease;
}

@keyframes fadeInDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Ensure sticky header works on all screen sizes */
@media (max-width: 768px) {
    .admin-top-nav {
        position: sticky !important;
        top: 0 !important;
        z-index: 1020 !important;
    }
}

/* Prevent content jumping when header becomes sticky */
.main-content {
    position: relative;
}

/* Enhanced sticky header shadow on scroll */
.admin-top-nav.scrolled {
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}