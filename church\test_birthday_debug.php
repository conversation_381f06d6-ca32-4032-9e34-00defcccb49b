<?php
require_once 'config.php';
require_once 'classes/BirthdayReminder.php';

// First, let's see what members exist
echo "<h2>Available Members:</h2>";
$query = "SELECT id, full_name, email FROM members LIMIT 10";
$stmt = $pdo->prepare($query);
$stmt->execute();
$members = $stmt->fetchAll(PDO::FETCH_ASSOC);

echo "<table border='1'>";
echo "<tr><th>ID</th><th>Name</th><th>Email</th></tr>";
foreach ($members as $m) {
    echo "<tr><td>" . $m['id'] . "</td><td>" . htmlspecialchars($m['full_name']) . "</td><td>" . htmlspecialchars($m['email']) . "</td></tr>";
}
echo "</table>";

// Test with the first available member
if (!empty($members)) {
    $member = $members[0];
    $memberId = $member['id'];
} else {
    echo "<p>No members found!</p>";
    exit;
}

if ($member) {
    echo "<h2>Testing Birthday Email for: " . htmlspecialchars($member['full_name']) . "</h2>";
    
    // Create birthday reminder instance
    $birthdayReminder = new BirthdayReminder($pdo);
    
    // Prepare member data
    $memberData = $birthdayReminder->prepareMemberDataWithImage($member);
    
    echo "<h3>Member Data:</h3>";
    echo "<pre>" . print_r($memberData, true) . "</pre>";
    
    // Get ALL birthday templates
    $query = "SELECT * FROM email_templates WHERE is_birthday_template = 1";
    $stmt = $pdo->prepare($query);
    $stmt->execute();
    $templates = $stmt->fetchAll(PDO::FETCH_ASSOC);

    echo "<h3>Available Birthday Templates:</h3>";
    foreach ($templates as $i => $tmpl) {
        echo "<p><strong>Template " . ($i+1) . ":</strong> " . htmlspecialchars($tmpl['template_name']) . " (ID: " . $tmpl['id'] . ")</p>";
    }

    // Test ALL templates
    foreach ($templates as $i => $template) {
        echo "<hr><h3>Testing Template " . ($i+1) . ": " . htmlspecialchars($template['template_name']) . " (ID: " . $template['id'] . ")</h3>";

        echo "<h4>Template Content (first 300 chars):</h4>";
        echo "<pre>" . htmlspecialchars(substr($template['content'], 0, 300)) . "</pre>";

        // Process template
        $subject = replaceTemplatePlaceholders($template['subject'], $memberData);
        $body = replaceTemplatePlaceholders($template['content'], $memberData);

        echo "<h4>Processed Subject:</h4>";
        echo "<pre>" . htmlspecialchars($subject) . "</pre>";

        echo "<h4>Processed Body (first 500 chars):</h4>";
        echo "<pre>" . htmlspecialchars(substr($body, 0, 500)) . "</pre>";

        // Check if placeholder still exists
        $placeholderExists = strpos($body, '{birthday_member_image_url}') !== false;
        echo "<h4>Placeholder {birthday_member_image_url} still exists: " . ($placeholderExists ? 'YES' : 'NO') . "</h4>";

        if ($placeholderExists) {
            echo "<p style='color: red;'><strong>ERROR: Placeholder was not replaced!</strong></p>";
        } else {
            echo "<p style='color: green;'><strong>SUCCESS: Placeholder was replaced!</strong></p>";
        }
    }

    if (empty($templates)) {
        echo "<p>No birthday templates found!</p>";
    }
} else {
    echo "<p>Member not found!</p>";
}
?>
