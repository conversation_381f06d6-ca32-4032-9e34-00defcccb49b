<?php
/**
 * BirthdayReminder Class
 * 
 * Handles birthday email functionality including sending reminders
 * and managing birthday notifications.
 */

class BirthdayReminder {
    private $pdo;
    private $adminEmail;
    private $failedEmails = [];
    private $sentEmails = [];
    private $trackingEnabled = true;
    private $emailType = 'birthday';

    public function __construct($pdo, $adminEmail = null) {
        $this->pdo = $pdo;
        $this->adminEmail = $adminEmail ?: '<EMAIL>';
    }

    /**
     * Add throttling delay between emails based on settings
     */
    private function addThrottlingDelay() {
        // Get configured delay from email settings, default to 3 seconds if not set
        $delay_seconds = 3; // Default delay

        try {
            $stmt = $this->pdo->prepare("SELECT setting_value FROM email_settings WHERE setting_key = 'email_sending_delay_seconds' LIMIT 1");
            $stmt->execute();
            $result = $stmt->fetch(PDO::FETCH_ASSOC);

            if ($result && !empty($result['setting_value'])) {
                $delay_seconds = floatval($result['setting_value']);
            }
        } catch (Exception $e) {
            // If there's an error, use the default delay
            error_log("Error fetching email delay setting: " . $e->getMessage());
        }

        // Add random variation to the delay (±20%)
        $variation = $delay_seconds * 0.2;
        $actual_delay = $delay_seconds + (mt_rand(-100, 100) / 100) * $variation;
        $actual_delay = max(0.5, $actual_delay); // Minimum 0.5 seconds

        // Convert to microseconds and sleep
        usleep(intval($actual_delay * 1000000));
    }

    /**
     * Get email batch size from settings
     */
    private function getEmailBatchSize() {
        // Get batch size from settings, default to 25 if not set
        $batch_size = 25; // Default batch size
        try {
            $stmt = $this->pdo->prepare("SELECT setting_value FROM email_settings WHERE setting_key = 'email_batch_size' LIMIT 1");
            $stmt->execute();
            $result = $stmt->fetch(PDO::FETCH_ASSOC);

            if ($result && !empty($result['setting_value'])) {
                $batch_size = intval($result['setting_value']);
                // Ensure batch size is valid (between 10 and 5000)
                $batch_size = max(10, min(5000, $batch_size));
            }
        } catch (Exception $e) {
            error_log("Error fetching email batch size setting: " . $e->getMessage());
        }

        return $batch_size;
    }

    public function getUpcomingBirthdays($daysAhead) {
        // This query compares only month and day parts of the dates
        $query = "SELECT * FROM members WHERE
                  MONTH(DATE_ADD(CURRENT_DATE, INTERVAL ? DAY)) = MONTH(birth_date) AND
                  DAY(DATE_ADD(CURRENT_DATE, INTERVAL ? DAY)) = DAY(birth_date)";
        $stmt = $this->pdo->prepare($query);
        $stmt->execute([$daysAhead, $daysAhead]);

        $members = $stmt->fetchAll();
        error_log("Found " . count($members) . " members with birthdays " . ($daysAhead == 0 ? "today" : "in $daysAhead days"));
        return $members;
    }

    /**
     * Prepare member data with proper image processing for email templates
     */
    public function prepareMemberDataWithImage($member) {
        // Start with the original member data
        $memberData = $member;

        // Generate site URL
        $siteUrl = defined('SITE_URL') ? SITE_URL :
            ((isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? "https" : "http") . "://" . $_SERVER['HTTP_HOST']);

        // Process member image
        $photoUrl = '';
        $memberImageHtml = '';

        if (!empty($member['image_path'])) {
            $photoUrl = $siteUrl . '/' . ltrim($member['image_path'], '/');
            $memberImageHtml = '<img src="' . $photoUrl . '" alt="' .
                htmlspecialchars($member['full_name'] ?? 'Member') .
                '" style="display:block; max-width:150px; height:auto; border-radius:50%; margin:15px auto;">';
        } else {
            $photoUrl = $siteUrl . '/assets/img/default-avatar.png';
            $memberImageHtml = '<img src="' . $photoUrl . '" alt="' .
                htmlspecialchars($member['full_name'] ?? 'Member') .
                '" style="display:block; max-width:150px; height:auto; border-radius:50%; margin:15px auto;">';
        }

        // Add image-related placeholders
        $memberData['member_image'] = $photoUrl; // Use URL - email processing will handle HTML
        $memberData['member_image_html'] = $memberImageHtml; // Keep HTML version for other uses
        $memberData['member_image_url'] = $photoUrl;
        $memberData['birthday_member_photo_url'] = $photoUrl;
        $memberData['birthday_member_image'] = $photoUrl;
        $memberData['birthday_member_image_url'] = $photoUrl; // Add missing placeholder

        // Store original image path for email embedding
        $memberData['_original_image_path'] = $member['image_path'] ?? null;

        // Add name variations for compatibility
        if (!empty($member['full_name'])) {
            $nameParts = explode(' ', $member['full_name'], 2);
            $memberData['first_name'] = $nameParts[0];
            $memberData['last_name'] = isset($nameParts[1]) ? $nameParts[1] : '';
            $memberData['birthday_member_name'] = $nameParts[0];
            $memberData['birthday_member_first_name'] = $nameParts[0];
            $memberData['birthday_member_full_name'] = $member['full_name'];
        }

        // Add birthday-specific data
        if (!empty($member['birth_date'])) {
            $memberData['birthday_date'] = date('F j', strtotime($member['birth_date']));
            $memberData['birthday_year'] = date('Y', strtotime($member['birth_date']));

            // Calculate age correctly
            try {
                $birth = new DateTime($member['birth_date']);
                $today = new DateTime();
                $age = $today->diff($birth)->y;
                $memberData['age'] = $age;
                $memberData['birthday_member_age'] = $age;
            } catch (Exception $e) {
                error_log("Error calculating age for member: " . $e->getMessage());
                $memberData['age'] = 'Unknown';
                $memberData['birthday_member_age'] = 'Unknown';
            }
        }

        // Add current date/time info
        $memberData['current_date'] = date('F j, Y');
        $memberData['current_year'] = date('Y');
        $memberData['current_time'] = date('g:i A');

        return $memberData;
    }

    public function sendBirthdayEmails($templateId = null, $daysRange = 0) {
        $this->failedEmails = [];
        $this->sentEmails = [];

        try {
            $totalSent = 0;
            $totalFailed = 0;

            // Get members with birthdays for the specified day range
            $members = $this->getUpcomingBirthdays($daysRange);

            // Get batch size for throttling
            $batchSize = $this->getEmailBatchSize();
            $batches = array_chunk($members, $batchSize);

            foreach ($batches as $batchIndex => $batch) {
                error_log("Processing birthday email batch " . ($batchIndex + 1) . " of " . count($batches) . " (batch size: " . count($batch) . ")");

                foreach ($batch as $member) {
                    if (empty($member['email'])) {
                        continue;
                    }

                    // Get template
                    if ($templateId) {
                        $stmt = $this->pdo->prepare("SELECT * FROM email_templates WHERE id = ?");
                        $stmt->execute([$templateId]);
                        $template = $stmt->fetch();
                    } else {
                        $stmt = $this->pdo->prepare("SELECT * FROM email_templates WHERE is_birthday_template = 1 ORDER BY RAND() LIMIT 1");
                        $stmt->execute();
                        $template = $stmt->fetch();
                    }

                    if (!$template) {
                        error_log("No birthday template found");
                        continue;
                    }

                    // Prepare member data with image processing
                    $memberData = $this->prepareMemberDataWithImage($member);

                    // CRITICAL FIX: For direct birthday emails, also set birthday member fields
                    // This allows templates with {birthday_member_image_url} to work correctly
                    // even when sending direct birthday emails to the celebrant
                    $memberData['birthday_member_name'] = $member['first_name'] ?? explode(' ', $member['full_name'])[0];
                    $memberData['birthday_member_full_name'] = $member['full_name'] ?? '';
                    $memberData['birthday_member_email'] = $member['email'] ?? '';
                    $memberData['birthday_member_phone'] = $member['phone_number'] ?? '';
                    $memberData['birthday_member_birth_date'] = $member['birth_date'] ?? '';

                    // Set birthday member image fields to the same as member image fields
                    if (isset($memberData['member_image_url'])) {
                        $memberData['birthday_member_photo_url'] = $memberData['member_image_url'];
                        $memberData['birthday_member_image'] = $memberData['member_image_url'];
                        $memberData['birthday_member_image_url'] = $memberData['member_image_url'];
                    }

                    // Set original image path for birthday member (same as member)
                    if (isset($memberData['_original_image_path'])) {
                        $memberData['_birthday_member_original_image_path'] = $memberData['_original_image_path'];
                    }

                    // Send email with member data for proper image processing
                    error_log("DEBUG: Member data before template processing: " . print_r($memberData, true));
                    error_log("DEBUG: Template content before processing: " . substr($template['content'], 0, 200));

                    $subject = replaceTemplatePlaceholders($template['subject'], $memberData);
                    $body = replaceTemplatePlaceholders($template['content'], $memberData);

                    error_log("DEBUG: Body after template processing: " . substr($body, 0, 300));
                    error_log("DEBUG: Checking if birthday_member_image_url placeholder still exists: " . (strpos($body, '{birthday_member_image_url}') !== false ? 'YES' : 'NO'));

                    // REMOVED: Problematic inline image wrapping logic that was creating nested <img> tags
                    // The template should already contain proper <img> tags with placeholders
                    // Let replaceTemplatePlaceholders() handle the placeholder replacement

                    if (sendEmail($member['email'], $member['full_name'], $subject, $body, true, $memberData)) {
                        $totalSent++;
                        $this->sentEmails[] = [
                            'member' => $member['full_name'],
                            'email' => $member['email']
                        ];
                        error_log("Successfully sent birthday email to {$member['full_name']} <{$member['email']}>");
                    } else {
                        $totalFailed++;
                        $this->failedEmails[] = [
                            'member' => $member['full_name'],
                            'email' => $member['email'],
                            'error' => 'Failed to send email'
                        ];
                        error_log("Failed to send birthday email to {$member['full_name']} <{$member['email']}>");
                    }

                    // Add throttling delay between emails (except for the last email in the last batch)
                    if (!($batchIndex === count($batches) - 1 && $member === end($batch))) {
                        $this->addThrottlingDelay();
                    }
                }

                // Add a longer pause between batches if there are more batches to process
                if ($batchIndex < count($batches) - 1) {
                    error_log("Completed batch " . ($batchIndex + 1) . ", pausing before next batch...");
                    sleep(2); // 2 second pause between batches
                }
            }
            
            return [
                'total_sent' => $totalSent,
                'total_failed' => $totalFailed,
                'sent' => $this->sentEmails,
                'failed' => $this->failedEmails
            ];
            
        } catch (Exception $e) {
            error_log("Error in sendBirthdayEmails: " . $e->getMessage());
            return [
                'total_sent' => 0,
                'total_failed' => 0,
                'error' => $e->getMessage()
            ];
        }
    }

    public function sendBirthdayReminders($templateId = null, $daysAhead = 3) {
        $this->failedEmails = [];
        $this->sentEmails = [];
        
        try {
            $totalSent = 0;
            $totalFailed = 0;
            
            // Get members with upcoming birthdays
            $members = $this->getUpcomingBirthdays($daysAhead);

            // Get batch size for throttling
            $batchSize = $this->getEmailBatchSize();
            $batches = array_chunk($members, $batchSize);

            foreach ($batches as $batchIndex => $batch) {
                error_log("Processing birthday reminder batch " . ($batchIndex + 1) . " of " . count($batches) . " (batch size: " . count($batch) . ")");

                foreach ($batch as $member) {
                if (empty($member['email'])) {
                    continue;
                }
                
                // Get template
                if ($templateId) {
                    $stmt = $this->pdo->prepare("SELECT * FROM email_templates WHERE id = ?");
                    $stmt->execute([$templateId]);
                    $template = $stmt->fetch();
                } else {
                    $stmt = $this->pdo->prepare("SELECT * FROM email_templates WHERE is_birthday_template = 0 ORDER BY RAND() LIMIT 1");
                    $stmt->execute();
                    $template = $stmt->fetch();
                }
                
                if (!$template) {
                    error_log("No reminder template found");
                    continue;
                }
                
                // Add days information to member data
                $member['days_until_birthday'] = $daysAhead;
                
                // Enhanced birthday placeholder calculations
                // Calculate birth date components
                $birth_month = date('m', strtotime($member['birth_date'] ?? 'now'));
                $birth_day = date('d', strtotime($member['birth_date'] ?? 'now'));
                $birth_year = date('Y', strtotime($member['birth_date'] ?? 'now'));
                $current_year = date('Y');
                $next_year = $current_year + 1;
                
                // Determine if birthday has passed this year already
                $this_year_birthday = $current_year . '-' . $birth_month . '-' . $birth_day;
                $next_year_birthday = $next_year . '-' . $birth_month . '-' . $birth_day;
                
                // Calculate upcoming birthday date and age
                $upcoming_date = strtotime($this_year_birthday) < time() ? $next_year_birthday : $this_year_birthday;

                // Use consistent DateTime-based age calculation
                try {
                    $birth = new DateTime($member['birth_date']);
                    $today = new DateTime();
                    $age = $today->diff($birth)->y;
                } catch (Exception $e) {
                    error_log("Error calculating age for member: " . $e->getMessage());
                    $age = 'Unknown';
                }
                
                // Prepare comprehensive set of placeholder values
                $member['birthday_date'] = date('F j', strtotime($member['birth_date'] ?? 'now'));
                $member['birthday_year'] = $birth_year;
                $member['current_year'] = $current_year;
                $member['current_date'] = date('F j, Y');
                $member['current_time'] = date('g:i A');
                $member['upcoming_birthday_date'] = date('F j, Y', strtotime($upcoming_date));
                $member['upcoming_birthday_day'] = date('l', strtotime($upcoming_date));
                $member['upcoming_birthday_formatted'] = date('l, F j, Y', strtotime($upcoming_date));
                $member['age'] = $age;
                $member['birthday_member_age'] = $age;
                $member['days_text'] = $daysAhead == 0 ? 'today' : 
                                      ($daysAhead == 1 ? 'tomorrow' : 
                                      "in $daysAhead days");
                
                // Add recipient-specific placeholders
                if (!isset($member['first_name']) && isset($member['full_name'])) {
                    $nameParts = explode(' ', $member['full_name'], 2);
                    $member['first_name'] = $nameParts[0];
                    $member['last_name'] = isset($nameParts[1]) ? $nameParts[1] : '';
                }
                
                $member['recipient_full_name'] = $member['full_name'] ?? '';
                $member['recipient_first_name'] = $member['first_name'] ?? '';
                $member['recipient_email'] = $member['email'] ?? '';
                $member['recipient_phone'] = $member['phone_number'] ?? '';
                
                // Also add these placeholders to "birthday_member_" equivalents
                $member['birthday_member_birth_date'] = $member['birthday_date'];
                $member['birthday_member_name'] = $member['first_name'] ?? '';
                $member['birthday_member_full_name'] = $member['full_name'] ?? '';

                // Prepare member data with image processing
                $memberData = $this->prepareMemberDataWithImage($member);

                // Send email with member data for proper image processing
                $subject = replaceTemplatePlaceholders($template['subject'], $memberData);
                $body = replaceTemplatePlaceholders($template['content'], $memberData);

                if (sendEmail($member['email'], $member['full_name'], $subject, $body, true, $memberData)) {
                    $totalSent++;
                    $this->sentEmails[] = [
                        'member' => $member['full_name'],
                        'email' => $member['email']
                    ];
                    error_log("Successfully sent birthday reminder to {$member['full_name']} <{$member['email']}>");
                } else {
                    $totalFailed++;
                    $this->failedEmails[] = [
                        'member' => $member['full_name'],
                        'email' => $member['email'],
                        'error' => 'Failed to send email'
                    ];
                    error_log("Failed to send birthday reminder to {$member['full_name']} <{$member['email']}>");
                }

                // Add throttling delay between emails (except for the last email in the last batch)
                if (!($batchIndex === count($batches) - 1 && $member === end($batch))) {
                    $this->addThrottlingDelay();
                }
            }

            // Add a longer pause between batches if there are more batches to process
            if ($batchIndex < count($batches) - 1) {
                error_log("Completed reminder batch " . ($batchIndex + 1) . ", pausing before next batch...");
                sleep(2); // 2 second pause between batches
            }
        }
            
            return [
                'total_sent' => $totalSent,
                'total_failed' => $totalFailed,
                'sent' => $this->sentEmails,
                'failed' => $this->failedEmails
            ];
            
        } catch (Exception $e) {
            error_log("Error in sendBirthdayReminders: " . $e->getMessage());
            return [
                'total_sent' => 0,
                'total_failed' => 0,
                'error' => $e->getMessage()
            ];
        }
    }
} 