<?php
require_once 'config.php';

echo "<h1>Testing Birthday Image Placeholder Fix - Direct Birthday Emails</h1>";

// Test 1: Direct birthday email (what BirthdayReminder class sends)
echo "<h2>Test 1: Direct Birthday Email (to celebrant)</h2>";

$testMemberData1 = [
    'full_name' => '<PERSON>',
    'first_name' => '<PERSON>',
    'email' => '<EMAIL>',
    'image_path' => 'uploads/profiles/test.jpg',
    'member_image_url' => 'http://localhost/campaign/church/uploads/profiles/test.jpg',
    '_original_image_path' => 'uploads/profiles/test.jpg',

    // NEW: Birthday member fields (same as member fields for direct emails)
    'birthday_member_name' => '<PERSON>',
    'birthday_member_full_name' => '<PERSON>',
    'birthday_member_photo_url' => 'http://localhost/campaign/church/uploads/profiles/test.jpg',
    'birthday_member_image' => 'http://localhost/campaign/church/uploads/profiles/test.jpg',
    'birthday_member_image_url' => 'http://localhost/campaign/church/uploads/profiles/test.jpg',
    '_birthday_member_original_image_path' => 'uploads/profiles/test.jpg'
];

$testTemplate1 = 'Happy Birthday {birthday_member_name}! Here is your photo: <img src="{birthday_member_image_url}" alt="{birthday_member_name}" />';

echo "<h3>Template:</h3>";
echo "<pre>" . htmlspecialchars($testTemplate1) . "</pre>";

$result1 = replaceTemplatePlaceholders($testTemplate1, $testMemberData1);

echo "<h3>Result:</h3>";
echo "<pre>" . htmlspecialchars($result1) . "</pre>";

// Check if the placeholder was replaced
if (strpos($result1, '{birthday_member_image_url}') !== false) {
    echo "<p style='color: red; font-weight: bold;'>❌ FAILED: Placeholder {birthday_member_image_url} was NOT replaced!</p>";
} else {
    echo "<p style='color: green; font-weight: bold;'>✅ SUCCESS: Placeholder {birthday_member_image_url} was replaced!</p>";
}

echo "<h3>HTML Preview:</h3>";
echo "<div style='border: 1px solid #ccc; padding: 10px; background: #f9f9f9;'>";
echo $result1;
echo "</div>";

echo "<hr>";

// Test 2: Notification email (to other members about someone's birthday)
echo "<h2>Test 2: Birthday Notification Email (to other members)</h2>";

$testMemberData2 = [
    'full_name' => 'Jane Smith',
    'first_name' => 'Jane',
    'email' => '<EMAIL>',

    // Birthday notification flags
    '_is_birthday_notification' => true,
    'birthday_member_name' => 'John',
    'birthday_member_full_name' => 'John Doe',
    'birthday_member_photo_url' => 'http://localhost/campaign/church/uploads/profiles/john.jpg',
    'birthday_member_image' => 'http://localhost/campaign/church/uploads/profiles/john.jpg',
    'birthday_member_image_url' => 'http://localhost/campaign/church/uploads/profiles/john.jpg',
    '_birthday_member_original_image_path' => 'uploads/profiles/john.jpg'
];

$testTemplate2 = 'Hi {first_name}! {birthday_member_name} has a birthday today! <img src="{birthday_member_image_url}" alt="{birthday_member_name}" />';

echo "<h3>Template:</h3>";
echo "<pre>" . htmlspecialchars($testTemplate2) . "</pre>";

$result2 = replaceTemplatePlaceholders($testTemplate2, $testMemberData2);

echo "<h3>Result:</h3>";
echo "<pre>" . htmlspecialchars($result2) . "</pre>";

// Check if the placeholder was replaced
if (strpos($result2, '{birthday_member_image_url}') !== false) {
    echo "<p style='color: red; font-weight: bold;'>❌ FAILED: Placeholder {birthday_member_image_url} was NOT replaced!</p>";
} else {
    echo "<p style='color: green; font-weight: bold;'>✅ SUCCESS: Placeholder {birthday_member_image_url} was replaced!</p>";
}

echo "<h3>HTML Preview:</h3>";
echo "<div style='border: 1px solid #ccc; padding: 10px; background: #f9f9f9;'>";
echo $result2;
echo "</div>";
?>
