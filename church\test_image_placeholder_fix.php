<?php
require_once 'config.php';

echo "<h1>Testing Birthday Image Placeholder Fix</h1>";

// Test data simulating a birthday notification
$testMemberData = [
    'full_name' => '<PERSON>',
    'first_name' => '<PERSON>',
    'email' => '<EMAIL>',
    'image_path' => 'uploads/profiles/test.jpg',
    
    // Birthday notification flags
    '_is_birthday_notification' => true,
    'birthday_member_name' => '<PERSON>',
    'birthday_member_full_name' => '<PERSON>',
    
    // Image URL fields (these should be set by the birthday email systems)
    'birthday_member_photo_url' => 'http://localhost/campaign/church/uploads/profiles/test.jpg',
    'birthday_member_image' => 'http://localhost/campaign/church/uploads/profiles/test.jpg',
    'birthday_member_image_url' => 'http://localhost/campaign/church/uploads/profiles/test.jpg',
    'member_image_url' => 'http://localhost/campaign/church/uploads/profiles/test.jpg',
    
    // Original image path for embedding
    '_birthday_member_original_image_path' => 'uploads/profiles/test.jpg'
];

// Test template with the problematic placeholder
$testTemplate = 'Happy Birthday {birthday_member_name}! Here is your photo: <img src="{birthday_member_image_url}" alt="{birthday_member_name}" />';

echo "<h2>Test Data:</h2>";
echo "<pre>" . print_r($testMemberData, true) . "</pre>";

echo "<h2>Template:</h2>";
echo "<pre>" . htmlspecialchars($testTemplate) . "</pre>";

echo "<h2>Processing Template...</h2>";
$result = replaceTemplatePlaceholders($testTemplate, $testMemberData);

echo "<h2>Result:</h2>";
echo "<pre>" . htmlspecialchars($result) . "</pre>";

// Check if the placeholder was replaced
if (strpos($result, '{birthday_member_image_url}') !== false) {
    echo "<p style='color: red; font-weight: bold;'>❌ FAILED: Placeholder {birthday_member_image_url} was NOT replaced!</p>";
} else {
    echo "<p style='color: green; font-weight: bold;'>✅ SUCCESS: Placeholder {birthday_member_image_url} was replaced!</p>";
}

// Check if the URL appears in the result
if (strpos($result, 'http://localhost/campaign/church/uploads/profiles/test.jpg') !== false) {
    echo "<p style='color: green; font-weight: bold;'>✅ SUCCESS: Image URL appears in the result!</p>";
} else {
    echo "<p style='color: red; font-weight: bold;'>❌ FAILED: Image URL does not appear in the result!</p>";
}

echo "<h2>HTML Preview:</h2>";
echo "<div style='border: 1px solid #ccc; padding: 10px; background: #f9f9f9;'>";
echo $result;
echo "</div>";
?>
