<?php
require_once 'config.php';

// Use the global PDO connection
global $pdo;

// First check the table structure
echo "<h2>Email Templates Table Structure</h2>";
$query = "DESCRIBE email_templates";
$stmt = $pdo->prepare($query);
$stmt->execute();
$columns = $stmt->fetchAll(PDO::FETCH_ASSOC);

echo "<table border='1'>";
echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
foreach ($columns as $column) {
    echo "<tr>";
    echo "<td>" . htmlspecialchars($column['Field']) . "</td>";
    echo "<td>" . htmlspecialchars($column['Type']) . "</td>";
    echo "<td>" . htmlspecialchars($column['Null']) . "</td>";
    echo "<td>" . htmlspecialchars($column['Key']) . "</td>";
    echo "<td>" . htmlspecialchars($column['Default']) . "</td>";
    echo "<td>" . htmlspecialchars($column['Extra']) . "</td>";
    echo "</tr>";
}
echo "</table>";

// Check birthday email templates
$query = "SELECT * FROM email_templates WHERE template_category = 'birthday' OR is_birthday_template = 1 ORDER BY id";
$stmt = $pdo->prepare($query);
$stmt->execute();
$templates = $stmt->fetchAll(PDO::FETCH_ASSOC);

echo "<h2>Birthday Email Templates</h2>";

foreach ($templates as $template) {
    echo "<h3>Template ID: " . $template['id'] . "</h3>";
    echo "<p><strong>Template Data:</strong></p>";
    echo "<pre>" . print_r($template, true) . "</pre>";
    
    // Check for nested img tags
    $content = $template['content'];
    $nested_img_count = substr_count($content, '<img src="<img');
    $placeholder_count = substr_count($content, '{birthday_member_image_url}');
    
    echo "<p><strong>Nested img tags found:</strong> $nested_img_count</p>";
    echo "<p><strong>birthday_member_image_url placeholders:</strong> $placeholder_count</p>";
    
    // Show first 500 characters of content
    echo "<p><strong>Content preview:</strong></p>";
    echo "<pre>" . htmlspecialchars(substr($content, 0, 500)) . "...</pre>";
    
    // Look for specific image-related content
    if (strpos($content, 'img') !== false) {
        echo "<p><strong>Image-related content:</strong></p>";
        preg_match_all('/<img[^>]*>/', $content, $matches);
        foreach ($matches[0] as $img_tag) {
            echo "<pre>" . htmlspecialchars($img_tag) . "</pre>";
        }
    }
    
    echo "<hr>";
}
?>
